2025-07-09 18:57:36,866 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_0f0a36aa4cd25b59'@'localhost'
2025-07-09 18:57:36,867 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_0f0a36aa4cd25b59`
2025-07-09 18:57:36,876 WARNING database DDL Query made to DB:
CREATE USER '_0f0a36aa4cd25b59'@'localhost' IDENTIFIED BY 'jSsyJcSNpc9Fq4zd'
2025-07-09 18:57:36,878 WARNING database DDL Query made to DB:
CREATE DATABASE `_0f0a36aa4cd25b59` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-07-09 18:57:37,184 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:37,193 WARNING database DDL Query made to DB:
create table __global_search(
				doctype varchar(100),
				name varchar(140),
				title varchar(140),
				content text,
				fulltext(content),
				route varchar(140),
				published int(1) not null default 0,
				unique `doctype_name` (doctype, name))
				COLLATE=utf8mb4_unicode_ci
				ENGINE=MyISAM
				CHARACTER SET=utf8mb4
2025-07-09 18:57:37,206 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:38,539 WARNING database DDL Query made to DB:
create table `tabDocType State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`color` varchar(140) default 'Blue',
`custom` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:38,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `non_negative` int(1) not null default 0, ADD COLUMN `is_virtual` int(1) not null default 0, ADD COLUMN `sort_options` int(1) not null default 0, ADD COLUMN `link_filters` json, ADD COLUMN `fetch_from` text, ADD COLUMN `show_on_timeline` int(1) not null default 0, ADD COLUMN `make_attachment_public` int(1) not null default 0, ADD COLUMN `documentation_url` varchar(140), ADD COLUMN `placeholder` varchar(140)
2025-07-09 18:57:38,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `precision` varchar(140), MODIFY `read_only_depends_on` longtext, MODIFY `label` varchar(140), MODIFY `mandatory_depends_on` longtext, MODIFY `fieldtype` varchar(140) default 'Data', MODIFY `fieldname` varchar(140), MODIFY `oldfieldname` varchar(140), MODIFY `oldfieldtype` varchar(140), MODIFY `collapsible_depends_on` longtext, MODIFY `print_width` varchar(10), MODIFY `depends_on` longtext, MODIFY `width` varchar(10)
2025-07-09 18:57:39,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` ADD COLUMN `if_owner` int(1) not null default 0, ADD COLUMN `select` int(1) not null default 0
2025-07-09 18:57:39,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` MODIFY `role` varchar(140)
2025-07-09 18:57:39,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-07-09 18:57:39,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` ADD COLUMN `parent_doctype` varchar(140), ADD COLUMN `table_fieldname` varchar(140), ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `is_child_table` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-07-09 18:57:39,320 WARNING database DDL Query made to DB:
create table `tabForm Tour Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ui_tour` int(1) not null default 0,
`is_table_field` int(1) not null default 0,
`title` varchar(140),
`parent_fieldname` varchar(140),
`fieldname` varchar(140),
`element_selector` varchar(140),
`parent_element_selector` varchar(140),
`description` longtext,
`ondemand_description` longtext,
`position` varchar(140) default 'Bottom',
`hide_buttons` int(1) not null default 0,
`popover_element` int(1) not null default 0,
`modal_trigger` int(1) not null default 0,
`offset_x` int(11) not null default 0,
`offset_y` int(11) not null default 0,
`next_on_click` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default '0',
`has_next_condition` int(1) not null default 0,
`next_step_condition` longtext,
`next_form_tour` varchar(140),
`child_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:39,446 WARNING database DDL Query made to DB:
create table `tabForm Tour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`view_name` varchar(140),
`workspace_name` varchar(140),
`list_name` varchar(140) default 'List',
`report_name` varchar(140),
`dashboard_name` varchar(140),
`new_document_form` int(1) not null default 0,
`page_name` varchar(140),
`reference_doctype` varchar(140),
`module` varchar(140),
`ui_tour` int(1) not null default 0,
`track_steps` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`save_on_complete` int(1) not null default 0,
`first_document` int(1) not null default 0,
`include_name_field` int(1) not null default 0,
`page_route` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:39,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `is_calendar_and_gantt` int(1) not null default 0, ADD COLUMN `quick_entry` int(1) not null default 0, ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `track_views` int(1) not null default 0, ADD COLUMN `queue_in_background` int(1) not null default 0, ADD COLUMN `documentation` varchar(140), ADD COLUMN `nsm_parent_field` varchar(140), ADD COLUMN `allow_events_in_timeline` int(1) not null default 0, ADD COLUMN `allow_auto_repeat` int(1) not null default 0, ADD COLUMN `make_attachments_public` int(1) not null default 0, ADD COLUMN `default_view` varchar(140), ADD COLUMN `force_re_route_to_default_view` int(1) not null default 0, ADD COLUMN `show_preview_popup` int(1) not null default 0, ADD COLUMN `default_email_template` varchar(140), ADD COLUMN `sender_name_field` varchar(140), ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `index_web_pages_for_search` int(1) not null default 1, ADD COLUMN `row_format` varchar(140) default 'Dynamic', ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-07-09 18:57:39,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `default_print_format` varchar(140), MODIFY `route` varchar(140), MODIFY `engine` varchar(140) default 'InnoDB', MODIFY `title_field` varchar(140), MODIFY `is_published_field` varchar(140), MODIFY `migration_hash` varchar(140), MODIFY `sort_field` varchar(140) default 'modified', MODIFY `module` varchar(140), MODIFY `search_fields` varchar(140), MODIFY `sort_order` varchar(140) default 'DESC', MODIFY `_user_tags` text, MODIFY `icon` varchar(140), MODIFY `document_type` varchar(140), MODIFY `restrict_to_domain` varchar(140), MODIFY `allow_rename` int(1) not null default 1, MODIFY `color` varchar(140), MODIFY `timeline_field` varchar(140), MODIFY `sender_field` varchar(140), MODIFY `autoname` varchar(140), MODIFY `image_field` varchar(140), MODIFY `subject_field` varchar(140), MODIFY `website_search_field` varchar(140)
2025-07-09 18:57:39,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD INDEX `module_index`(`module`)
2025-07-09 18:57:39,961 WARNING database DDL Query made to DB:
create table `tabRole` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_name` varchar(140) unique,
`home_page` varchar(140),
`restrict_to_domain` varchar(140),
`disabled` int(1) not null default 0,
`is_custom` int(1) not null default 0,
`desk_access` int(1) not null default 1,
`two_factor_auth` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,111 WARNING database DDL Query made to DB:
create table `tabHas Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,291 WARNING database DDL Query made to DB:
create table `tabCustom Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`dt` varchar(140),
`module` varchar(140),
`label` varchar(140),
`placeholder` varchar(140),
`fieldname` varchar(140),
`insert_after` varchar(140),
`length` int(11) not null default 0,
`link_filters` json,
`fieldtype` varchar(140) default 'Data',
`precision` varchar(140),
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`collapsible_depends_on` longtext,
`default` text,
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`read_only` int(1) not null default 0,
`ignore_user_permissions` int(1) not null default 0,
`hidden` int(1) not null default 0,
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`no_copy` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`search_index` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`translatable` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`description` text,
`permlevel` int(11) not null default 0,
`width` varchar(140),
`columns` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `dt`(`dt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,398 WARNING database DDL Query made to DB:
create table `tabProperty Setter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`doctype_or_field` varchar(140),
`doc_type` varchar(140),
`field_name` varchar(140),
`row_name` varchar(140),
`module` varchar(140),
`property` varchar(140),
`property_type` varchar(140),
`value` text,
`default_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `doc_type`(`doc_type`),
index `field_name`(`field_name`),
index `property`(`property`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,586 WARNING database DDL Query made to DB:
create table `tabWeb Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`doc_type` varchar(140),
`module` varchar(140),
`is_standard` int(1) not null default 0,
`introduction_text` longtext,
`anonymous` int(1) not null default 0,
`login_required` int(1) not null default 0,
`apply_document_permissions` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`allow_multiple` int(1) not null default 0,
`allow_delete` int(1) not null default 0,
`allow_incomplete` int(1) not null default 0,
`allow_comments` int(1) not null default 0,
`allow_print` int(1) not null default 0,
`print_format` varchar(140),
`max_attachment_size` int(11) not null default 0,
`show_attachments` int(1) not null default 0,
`allowed_embedding_domains` text,
`condition_json` json,
`show_list` int(1) not null default 0,
`list_title` varchar(140),
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`button_label` varchar(140) default 'Save',
`banner_image` text,
`breadcrumbs` longtext,
`success_title` varchar(140),
`success_url` varchar(140),
`success_message` text,
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`client_script` longtext,
`custom_css` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,692 WARNING database DDL Query made to DB:
create table `tabWeb Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Section',
`standard` int(1) not null default 0,
`module` varchar(140),
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,806 WARNING database DDL Query made to DB:
create table `tabWeb Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
`allow_read_on_all_link_options` int(1) not null default 0,
`reqd` int(1) not null default 0,
`read_only` int(1) not null default 0,
`show_in_filter` int(1) not null default 0,
`hidden` int(1) not null default 0,
`options` text,
`max_length` int(11) not null default 0,
`max_value` int(11) not null default 0,
`precision` varchar(140),
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`description` text,
`default` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:40,888 WARNING database DDL Query made to DB:
create table `tabPortal Menu Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`enabled` int(1) not null default 0,
`route` varchar(140),
`reference_doctype` varchar(140),
`role` varchar(140),
`target` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,006 WARNING database DDL Query made to DB:
create table `tabNumber Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`label` varchar(140),
`type` varchar(140),
`report_name` varchar(140),
`method` varchar(140),
`function` varchar(140),
`aggregate_function_based_on` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`report_field` varchar(140),
`report_function` varchar(140),
`is_public` int(1) not null default 0,
`currency` varchar(140),
`filters_config` longtext,
`show_percentage_stats` int(1) not null default 1,
`stats_time_interval` varchar(140) default 'Daily',
`filters_json` longtext,
`dynamic_filters_json` longtext,
`color` varchar(140),
`background_color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,180 WARNING database DDL Query made to DB:
create table `tabDashboard Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_name` varchar(140) unique,
`chart_type` varchar(140),
`report_name` varchar(140),
`use_report_chart` int(1) not null default 0,
`x_field` varchar(140),
`source` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`based_on` varchar(140),
`value_based_on` varchar(140),
`group_by_type` varchar(140) default 'Count',
`group_by_based_on` varchar(140),
`aggregate_function_based_on` varchar(140),
`number_of_groups` int(11) not null default 0,
`is_public` int(1) not null default 0,
`heatmap_year` varchar(140),
`timespan` varchar(140),
`from_date` date,
`to_date` date,
`time_interval` varchar(140),
`timeseries` int(1) not null default 0,
`type` varchar(140) default 'Line',
`currency` varchar(140),
`filters_json` longtext,
`dynamic_filters_json` longtext,
`custom_options` longtext,
`color` varchar(140),
`last_synced_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,285 WARNING database DDL Query made to DB:
create table `tabDashboard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dashboard_name` varchar(140) unique,
`is_default` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_options` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,359 WARNING database DDL Query made to DB:
create table `tabOnboarding Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,482 WARNING database DDL Query made to DB:
create table `tabOnboarding Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`is_complete` int(1) not null default 0,
`is_skipped` int(1) not null default 0,
`description` longtext,
`intro_video_url` varchar(140),
`action` varchar(140),
`action_label` varchar(140),
`reference_document` varchar(140),
`show_full_form` int(1) not null default 0,
`show_form_tour` int(1) not null default 0,
`form_tour` varchar(140),
`is_single` int(1) not null default 0,
`reference_report` varchar(140),
`report_reference_doctype` varchar(140),
`report_type` varchar(140),
`report_description` varchar(140),
`path` varchar(140),
`callback_title` varchar(140),
`callback_message` text,
`validate_action` int(1) not null default 1,
`field` varchar(140),
`value_to_validate` varchar(140),
`video_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,549 WARNING database DDL Query made to DB:
create table `tabOnboarding Step Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`step` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,639 WARNING database DDL Query made to DB:
create table `tabModule Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`module` varchar(140),
`success_message` varchar(140),
`documentation_url` varchar(140),
`is_complete` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,750 WARNING database DDL Query made to DB:
create table `tabWorkspace Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Link',
`label` varchar(140),
`icon` varchar(140),
`description` longtext,
`hidden` int(1) not null default 0,
`link_type` varchar(140),
`link_to` varchar(140),
`report_ref_doctype` varchar(140),
`dependencies` varchar(140),
`only_for` varchar(140),
`onboard` int(1) not null default 0,
`is_query_report` int(1) not null default 0,
`link_count` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,819 WARNING database DDL Query made to DB:
create table `tabWorkspace Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chart_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,918 WARNING database DDL Query made to DB:
create table `tabWorkspace Shortcut` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`link_to` varchar(140),
`url` varchar(140),
`doc_view` varchar(140),
`kanban_board` varchar(140),
`label` varchar(140),
`icon` varchar(140),
`restrict_to_domain` varchar(140),
`report_ref_doctype` varchar(140),
`stats_filter` longtext,
`color` varchar(140),
`format` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:41,994 WARNING database DDL Query made to DB:
create table `tabWorkspace Quick List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`label` varchar(140),
`quick_list_filter` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,064 WARNING database DDL Query made to DB:
create table `tabWorkspace Number Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number_card_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,136 WARNING database DDL Query made to DB:
create table `tabWorkspace Custom Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`custom_block_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,267 WARNING database DDL Query made to DB:
create table `tabWorkspace` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140) unique,
`title` varchar(140),
`sequence_id` decimal(21,9) not null default 0,
`for_user` varchar(140),
`parent_page` varchar(140),
`module` varchar(140),
`icon` varchar(140),
`indicator_color` varchar(140),
`restrict_to_domain` varchar(140),
`hide_custom` int(1) not null default 0,
`public` int(1) not null default 0,
`is_hidden` int(1) not null default 0,
`content` longtext default '[]',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `restrict_to_domain`(`restrict_to_domain`),
index `public`(`public`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,401 WARNING database DDL Query made to DB:
create table `tabPage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_page` int(1) not null default 0,
`page_name` varchar(140) unique,
`title` varchar(140),
`icon` varchar(140),
`module` varchar(140),
`restrict_to_domain` varchar(140),
`standard` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,536 WARNING database DDL Query made to DB:
create table `tabReport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report_name` varchar(140) unique,
`ref_doctype` varchar(140),
`reference_report` varchar(140),
`is_standard` varchar(140),
`module` varchar(140),
`report_type` varchar(140),
`letter_head` varchar(140),
`add_total_row` int(1) not null default 0,
`disabled` int(1) not null default 0,
`prepared_report` int(1) not null default 0,
`add_translate_data` int(1) not null default 0,
`timeout` int(11) not null default 0,
`query` longtext,
`report_script` longtext,
`javascript` longtext,
`json` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,651 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140) unique,
`module` varchar(140),
`timeseries` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,785 WARNING database DDL Query made to DB:
create table `tabPrint Format` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`doc_type` varchar(140),
`module` varchar(140),
`default_print_language` varchar(140),
`standard` varchar(140) default 'No',
`custom_format` int(1) not null default 0,
`disabled` int(1) not null default 0,
`pdf_generator` varchar(140) default 'wkhtmltopdf',
`print_format_type` varchar(140) default 'Jinja',
`raw_printing` int(1) not null default 0,
`html` longtext,
`raw_commands` longtext,
`margin_top` decimal(21,9) not null default 15.0,
`margin_bottom` decimal(21,9) not null default 15.0,
`margin_left` decimal(21,9) not null default 15.0,
`margin_right` decimal(21,9) not null default 15.0,
`align_labels_right` int(1) not null default 0,
`show_section_headings` int(1) not null default 0,
`line_breaks` int(1) not null default 0,
`absolute_value` int(1) not null default 0,
`font_size` int(11) not null default 14,
`font` varchar(140),
`page_number` varchar(140) default 'Hide',
`css` longtext,
`format_data` longtext,
`print_format_builder` int(1) not null default 0,
`print_format_builder_beta` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:42,958 WARNING database DDL Query made to DB:
create table `tabWeb Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`dynamic_route` int(1) not null default 0,
`published` int(1) not null default 1,
`module` varchar(140),
`content_type` varchar(140) default 'Page Builder',
`slideshow` varchar(140),
`dynamic_template` int(1) not null default 0,
`main_section` longtext,
`main_section_md` longtext,
`main_section_html` longtext,
`context_script` longtext,
`javascript` longtext,
`insert_style` int(1) not null default 0,
`text_align` varchar(140),
`css` longtext,
`full_width` int(1) not null default 1,
`show_title` int(1) not null default 0,
`start_date` datetime(6),
`end_date` datetime(6),
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`enable_comments` int(1) not null default 0,
`header` longtext,
`breadcrumbs` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,089 WARNING database DDL Query made to DB:
create table `tabWebsite Theme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`theme` varchar(140) unique,
`module` varchar(140) default 'Website',
`custom` int(1) not null default 1,
`google_font` varchar(140),
`font_size` varchar(140),
`font_properties` varchar(140) default 'wght@300;400;500;600;700;800',
`button_rounded_corners` int(1) not null default 1,
`button_shadows` int(1) not null default 0,
`button_gradients` int(1) not null default 0,
`primary_color` varchar(140),
`text_color` varchar(140),
`light_color` varchar(140),
`dark_color` varchar(140),
`background_color` varchar(140),
`custom_overrides` longtext,
`custom_scss` longtext,
`theme_scss` longtext,
`theme_url` varchar(140),
`js` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,236 WARNING database DDL Query made to DB:
create table `tabNotification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`channel` varchar(140) default 'Email',
`slack_webhook_url` varchar(140),
`subject` varchar(140),
`event` varchar(140),
`document_type` varchar(140),
`method` varchar(140),
`date_changed` varchar(140),
`days_in_advance` int(11) not null default 0,
`value_changed` varchar(140),
`sender` varchar(140),
`send_system_notification` int(1) not null default 0,
`sender_email` varchar(140),
`condition` longtext,
`set_property_after_alert` varchar(140),
`property_value` varchar(140),
`send_to_all_assignees` int(1) not null default 0,
`message_type` varchar(140) default 'Markdown',
`message` longtext default 'Add your message here',
`attach_print` int(1) not null default 0,
`print_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `event`(`event`),
index `document_type`(`document_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,341 WARNING database DDL Query made to DB:
create table `tabPrint Style` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_style_name` varchar(140) unique,
`disabled` int(1) not null default 0,
`standard` int(1) not null default 0,
`css` longtext,
`preview` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,433 WARNING database DDL Query made to DB:
create table `tabClient Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`view` varchar(140) default 'Form',
`module` varchar(140),
`enabled` int(1) not null default 0,
`script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,556 WARNING database DDL Query made to DB:
create table `tabServer Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`script_type` varchar(140),
`reference_doctype` varchar(140),
`event_frequency` varchar(140),
`cron_format` varchar(140),
`doctype_event` varchar(140),
`api_method` varchar(140),
`allow_guest` int(1) not null default 0,
`module` varchar(140),
`disabled` int(1) not null default 0,
`script` longtext,
`enable_rate_limit` int(1) not null default 0,
`rate_limit_count` int(11) not null default 5,
`rate_limit_seconds` int(11) not null default 86400,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `module`(`module`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,646 WARNING database DDL Query made to DB:
create table `tabDynamic Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`link_doctype` varchar(140),
`link_name` varchar(140),
`link_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabDynamic Link`
				ADD INDEX IF NOT EXISTS `link_doctype_link_name_index`(link_doctype, link_name)
2025-07-09 18:57:43,742 WARNING database DDL Query made to DB:
create table `tabDomain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`domain` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,864 WARNING database DDL Query made to DB:
create table `tabUser Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:43,947 WARNING database DDL Query made to DB:
create table `tabModule Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_profile_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:44,090 WARNING database DDL Query made to DB:
create table `tabUser Social Login` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`provider` varchar(140),
`username` varchar(140),
`userid` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:44,189 WARNING database DDL Query made to DB:
create table `tabError Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`seen` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`method` varchar(140),
`error` longtext,
`trace_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:44,266 WARNING database DDL Query made to DB:
create table `tabPackage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140),
`readme` longtext,
`license_type` varchar(140),
`license` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:44,351 WARNING database DDL Query made to DB:
create table `tabRole Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_profile` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:44,505 WARNING database DDL Query made to DB:
create table `tabReport Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldtype` varchar(140),
`fieldname` varchar(140),
`mandatory` int(1) not null default 0,
`wildcard_filter` int(1) not null default 0,
`options` text,
`default` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:44,588 WARNING database DDL Query made to DB:
create table `tabDeleted Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`deleted_name` varchar(140),
`deleted_doctype` varchar(140),
`restored` int(1) not null default 0,
`new_name` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,036 WARNING database DDL Query made to DB:
create table `tabComment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`comment_type` varchar(140) default 'Comment',
`comment_email` varchar(140),
`subject` text,
`comment_by` varchar(140),
`published` int(1) not null default 0,
`seen` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`content` longtext,
`ip_address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabComment`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-07-09 18:57:45,160 WARNING database DDL Query made to DB:
create table `tabPackage Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attach_package` text,
`activate` int(1) not null default 0,
`force` int(1) not null default 0,
`log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue` MODIFY `defkey` varchar(140)
2025-07-09 18:57:45,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue`
				ADD INDEX IF NOT EXISTS `defaultvalue_parent_parenttype_index`(parent, parenttype)
2025-07-09 18:57:45,370 WARNING database DDL Query made to DB:
create table `tabSuccess Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140) unique,
`first_success_message` varchar(140) default 'Congratulations on first creations',
`message` varchar(140) default 'Successfully created',
`next_actions` varchar(140),
`action_timeout` int(11) not null default 7,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,471 WARNING database DDL Query made to DB:
create table `tabUser Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`role` varchar(140),
`apply_user_permission_on` varchar(140),
`user_id_field` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,710 WARNING database DDL Query made to DB:
create table `tabData Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`import_type` varchar(140),
`import_file` text,
`payload_count` int(11) not null default 0,
`google_sheets_url` varchar(140),
`status` varchar(140) default 'Pending',
`submit_after_import` int(1) not null default 0,
`mute_emails` int(1) not null default 1,
`template_options` longtext,
`template_warnings` longtext,
`show_failed_logs` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,779 WARNING database DDL Query made to DB:
create table `tabLog Setting User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140) unique,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,850 WARNING database DDL Query made to DB:
create table `tabLogs To Clear` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`days` int(11) not null default 30,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:45,986 WARNING database DDL Query made to DB:
create table `tabSession Default` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,066 WARNING database DDL Query made to DB:
create table `tabInstalled Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`app_name` varchar(140),
`app_version` varchar(140),
`git_branch` varchar(140),
`has_setup_wizard` int(1) not null default 0,
`is_setup_complete` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,158 WARNING database DDL Query made to DB:
create table `tabScheduled Job Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`stopped` int(1) not null default 0,
`method` varchar(140),
`server_script` varchar(140),
`scheduler_event` varchar(140),
`frequency` varchar(140),
`cron_format` varchar(140),
`create_log` int(1) not null default 0,
`last_execution` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `server_script`(`server_script`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,289 WARNING database DDL Query made to DB:
create table `tabScheduler Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_against` varchar(140),
`method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,391 WARNING database DDL Query made to DB:
create table `tabAccess Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`export_from` varchar(140),
`user` varchar(140),
`reference_document` varchar(140),
`timestamp` datetime(6),
`file_type` varchar(140),
`method` varchar(140),
`report_name` varchar(140),
`filters` longtext,
`page` longtext,
`columns` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,507 WARNING database DDL Query made to DB:
create table `tabUser Type Module` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,583 WARNING database DDL Query made to DB:
create table `tabView Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`viewed_by` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:46,747 WARNING database DDL Query made to DB:
create table `tabCommunication` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`communication_medium` varchar(140),
`sender` varchar(255),
`recipients` longtext,
`cc` longtext,
`bcc` longtext,
`phone_no` varchar(140),
`delivery_status` varchar(140),
`content` longtext,
`text_content` longtext,
`communication_type` varchar(140) default 'Communication',
`comment_type` varchar(140),
`status` varchar(140),
`sent_or_received` varchar(140),
`communication_date` datetime(6),
`read_receipt` int(1) not null default 0,
`send_after` datetime(6),
`sender_full_name` varchar(140),
`read_by_recipient` int(1) not null default 0,
`read_by_recipient_on` datetime(6),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`email_account` varchar(140),
`in_reply_to` varchar(140),
`user` varchar(140),
`email_template` varchar(140),
`unread_notification_sent` int(1) not null default 0,
`seen` int(1) not null default 0,
`_user_tags` text,
`message_id` text,
`uid` int(11) not null default 0,
`imap_folder` varchar(140),
`email_status` varchar(140),
`has_attachment` int(1) not null default 0,
`rating` int(11) not null default 0,
`feedback_request` varchar(140),
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `reference_owner`(`reference_owner`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-07-09 18:57:47,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `status_communication_type_index`(status, communication_type)
2025-07-09 18:57:47,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `message_id_index`(message_id(140))
2025-07-09 18:57:47,335 WARNING database DDL Query made to DB:
create table `tabCustom DocPerm` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent` varchar(140),
`role` varchar(140),
`if_owner` int(1) not null default 0,
`permlevel` int(11) not null default 0,
`select` int(1) not null default 0,
`read` int(1) not null default 1,
`write` int(1) not null default 0,
`create` int(1) not null default 0,
`delete` int(1) not null default 0,
`submit` int(1) not null default 0,
`cancel` int(1) not null default 0,
`amend` int(1) not null default 0,
`report` int(1) not null default 0,
`export` int(1) not null default 1,
`import` int(1) not null default 0,
`share` int(1) not null default 0,
`print` int(1) not null default 0,
`email` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent`(`parent`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,475 WARNING database DDL Query made to DB:
create table `tabScheduled Job Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`scheduled_job_type` varchar(140),
`details` longtext,
`debug_log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,558 WARNING database DDL Query made to DB:
create table `tabVersion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`docname` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion`
				ADD INDEX IF NOT EXISTS `ref_doctype_docname_index`(ref_doctype, docname)
2025-07-09 18:57:47,673 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`disabled` int(1) not null default 0,
`priority` int(11) not null default 0,
`prefix` varchar(140),
`counter` int(11) not null default 0,
`prefix_digits` int(11) not null default 5,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,750 WARNING database DDL Query made to DB:
create table `tabSMS Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
`value` varchar(255),
`header` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,827 WARNING database DDL Query made to DB:
create table `tabUser Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_account` varchar(140),
`email_id` varchar(140),
`awaiting_password` int(1) not null default 0,
`used_oauth` int(1) not null default 0,
`enable_outgoing` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:47,962 WARNING database DDL Query made to DB:
create table `tabHas Domain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`domain` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,036 WARNING database DDL Query made to DB:
create table `tabCommunication Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`link_doctype` varchar(140),
`link_name` varchar(140),
`link_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Link`
				ADD INDEX IF NOT EXISTS `link_doctype_link_name_index`(link_doctype, link_name)
2025-07-09 18:57:48,150 WARNING database DDL Query made to DB:
create table `tabLanguage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`language_code` varchar(140) unique,
`language_name` varchar(140),
`flag` varchar(140),
`based_on` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,247 WARNING database DDL Query made to DB:
create table `tabTranslation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`contributed` int(1) not null default 0,
`language` varchar(140),
`source_text` longtext,
`context` varchar(140),
`translated_text` longtext,
`contribution_status` varchar(140),
`contribution_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `language`(`language`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,382 WARNING database DDL Query made to DB:
create table `tabData Import Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`data_import` varchar(140),
`row_indexes` longtext,
`success` int(1) not null default 0,
`docname` varchar(140),
`messages` longtext,
`exception` text,
`log_index` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,477 WARNING database DDL Query made to DB:
create table `tabSubmission Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`job_id` varchar(140),
`ended_at` datetime(6),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`exception` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_docname`(`ref_docname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,579 WARNING database DDL Query made to DB:
create table `tabDocShare` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`share_doctype` varchar(140),
`share_name` varchar(140),
`read` int(1) not null default 0,
`write` int(1) not null default 0,
`share` int(1) not null default 0,
`submit` int(1) not null default 0,
`everyone` int(1) not null default 0,
`notify_by_email` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `share_doctype`(`share_doctype`),
index `share_name`(`share_name`),
index `everyone`(`everyone`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare`
				ADD INDEX IF NOT EXISTS `user_share_doctype_index`(user, share_doctype)
2025-07-09 18:57:48,650 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare`
				ADD INDEX IF NOT EXISTS `share_doctype_share_name_index`(share_doctype, share_name)
2025-07-09 18:57:48,746 WARNING database DDL Query made to DB:
create table `tabActivity Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`content` longtext,
`communication_date` datetime(6),
`ip_address` varchar(140),
`operation` varchar(140),
`status` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`timeline_doctype` varchar(140),
`timeline_name` varchar(140),
`link_doctype` varchar(140),
`link_name` varchar(140),
`user` varchar(140),
`full_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-07-09 18:57:48,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `timeline_doctype_timeline_name_index`(timeline_doctype, timeline_name)
2025-07-09 18:57:48,878 WARNING database DDL Query made to DB:
create table `tabCustom Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`page` varchar(140),
`report` varchar(140),
`ref_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:48,947 WARNING database DDL Query made to DB:
create table `tabUser Group Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,016 WARNING database DDL Query made to DB:
create table `tabBlock Module` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,118 WARNING database DDL Query made to DB:
create table `tabUser Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`is_custom` int(1) not null default 0,
`read` int(1) not null default 1,
`write` int(1) not null default 0,
`create` int(1) not null default 0,
`submit` int(1) not null default 0,
`cancel` int(1) not null default 0,
`amend` int(1) not null default 0,
`delete` int(1) not null default 0,
`email` int(1) not null default 1,
`share` int(1) not null default 1,
`print` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,196 WARNING database DDL Query made to DB:
create table `tabReport Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`label` varchar(140),
`fieldtype` varchar(140),
`options` varchar(140),
`width` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,386 WARNING database DDL Query made to DB:
create table `tabPrepared Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Queued',
`report_name` varchar(140),
`job_id` varchar(140),
`report_end_time` datetime(6),
`peak_memory_usage` int(11) not null default 0,
`error_message` text,
`filters` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `report_name`(`report_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,553 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`field` varchar(140),
`condition` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,834 WARNING database DDL Query made to DB:
create table `tabUser` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`email` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`username` varchar(140) unique,
`language` varchar(140),
`time_zone` varchar(140),
`send_welcome_email` int(1) not null default 1,
`unsubscribed` int(1) not null default 0,
`user_image` text,
`role_profile_name` varchar(140),
`module_profile` varchar(140),
`home_settings` longtext,
`gender` varchar(140),
`birth_date` date,
`interest` text,
`phone` varchar(140),
`location` varchar(140),
`bio` text,
`mobile_no` varchar(140) unique,
`mute_sounds` int(1) not null default 0,
`desk_theme` varchar(140),
`banner_image` text,
`search_bar` int(1) not null default 1,
`notifications` int(1) not null default 1,
`list_sidebar` int(1) not null default 1,
`bulk_actions` int(1) not null default 1,
`view_switcher` int(1) not null default 1,
`form_sidebar` int(1) not null default 1,
`timeline` int(1) not null default 1,
`dashboard` int(1) not null default 1,
`new_password` text,
`logout_all_sessions` int(1) not null default 1,
`reset_password_key` varchar(140),
`last_reset_password_key_generated_on` datetime(6),
`last_password_reset_date` date,
`redirect_url` text,
`document_follow_notify` int(1) not null default 0,
`document_follow_frequency` varchar(140) default 'Daily',
`follow_created_documents` int(1) not null default 0,
`follow_commented_documents` int(1) not null default 0,
`follow_liked_documents` int(1) not null default 0,
`follow_assigned_documents` int(1) not null default 0,
`follow_shared_documents` int(1) not null default 0,
`email_signature` text,
`thread_notify` int(1) not null default 1,
`send_me_a_copy` int(1) not null default 0,
`allowed_in_mentions` int(1) not null default 1,
`default_workspace` varchar(140),
`default_app` varchar(140),
`simultaneous_sessions` int(11) not null default 2,
`restrict_ip` text,
`last_ip` varchar(140),
`login_after` int(11) not null default 0,
`user_type` varchar(140) default 'System User',
`last_active` datetime(6),
`login_before` int(11) not null default 0,
`bypass_restrict_ip_check_if_2fa_enabled` int(1) not null default 0,
`last_login` varchar(140),
`last_known_versions` text,
`api_key` varchar(140) unique,
`api_secret` text,
`onboarding_status` text default '{}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `last_active`(`last_active`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:49,973 WARNING database DDL Query made to DB:
create table `tabNavbar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_label` varchar(140),
`item_type` varchar(140),
`route` varchar(140),
`action` varchar(140),
`hidden` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:50,061 WARNING database DDL Query made to DB:
create table `tabPackage Release` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package` varchar(140),
`publish` int(1) not null default 0,
`path` text,
`major` int(11) not null default 0,
`minor` int(11) not null default 0,
`patch` int(11) not null default 0,
`release_notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:50,145 WARNING database DDL Query made to DB:
create table `tabModule Def` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_name` varchar(140) unique,
`custom` int(1) not null default 0,
`package` varchar(140),
`app_name` varchar(140),
`restrict_to_domain` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:50,439 WARNING database DDL Query made to DB:
create table `tabPatch Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patch` longtext,
`skipped` int(1) not null default 0,
`traceback` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:50,586 WARNING database DDL Query made to DB:
create table `tabAmended Document Naming Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`action` varchar(140) default 'Amend Counter',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:50,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` ADD COLUMN `is_private` int(1) not null default 0, ADD COLUMN `file_type` varchar(140), ADD COLUMN `is_home_folder` int(1) not null default 0, ADD COLUMN `is_attachments_folder` int(1) not null default 0, ADD COLUMN `thumbnail_url` text, ADD COLUMN `folder` varchar(255), ADD COLUMN `is_folder` int(1) not null default 0, ADD COLUMN `attached_to_field` varchar(140), ADD COLUMN `old_parent` varchar(140), ADD COLUMN `content_hash` varchar(140), ADD COLUMN `uploaded_to_dropbox` int(1) not null default 0, ADD COLUMN `uploaded_to_google_drive` int(1) not null default 0, ADD COLUMN `_user_tags` text, ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-07-09 18:57:50,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` MODIFY `file_url` longtext, MODIFY `file_size` bigint(20) not null default 0, MODIFY `attached_to_doctype` varchar(140), MODIFY `file_name` varchar(140), MODIFY `attached_to_name` varchar(140)
2025-07-09 18:57:50,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` DROP INDEX `attached_to_doctype`, DROP INDEX `attached_to_name`
2025-07-09 18:57:50,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `attached_to_doctype_attached_to_name_index`(attached_to_doctype, attached_to_name)
2025-07-09 18:57:50,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-07-09 18:57:50,896 WARNING database DDL Query made to DB:
create table `tabUser Select Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:51,001 WARNING database DDL Query made to DB:
create table `tabUser Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`allow` varchar(140),
`for_value` varchar(140),
`is_default` int(1) not null default 0,
`apply_to_all_doctypes` int(1) not null default 1,
`applicable_for` varchar(140),
`hide_descendants` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:51,085 WARNING database DDL Query made to DB:
create table `tabDocument Share Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`key` varchar(140),
`expires_on` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `reference_docname`(`reference_docname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:51,186 WARNING database DDL Query made to DB:
create table `tabTransaction Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`row_index` varchar(140),
`reference_doctype` varchar(140),
`document_name` varchar(140),
`timestamp` datetime(6),
`checksum_version` varchar(140),
`previous_hash` text,
`transaction_hash` text,
`chaining_hash` text,
`data` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:51,830 WARNING database DDL Query made to DB:
create table `tabWebsite Slideshow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`slideshow_name` varchar(140) unique,
`header` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,170 WARNING database DDL Query made to DB:
create table `tabAbout Us Team Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`full_name` varchar(140),
`image_link` text,
`bio` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,243 WARNING database DDL Query made to DB:
create table `tabMarketing Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,382 WARNING database DDL Query made to DB:
create table `tabWebsite Sidebar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,464 WARNING database DDL Query made to DB:
create table `tabPersonal Data Deletion Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email` varchar(140),
`status` varchar(140) default 'Pending Verification',
`anonymization_matrix` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,574 WARNING database DDL Query made to DB:
create table `tabHelp Article` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`category` varchar(140),
`published` int(1) not null default 0,
`author` varchar(140) default 'user_fullname',
`level` varchar(140),
`content` longtext,
`likes` int(11) not null default 0,
`route` varchar(140),
`helpful` int(11) not null default 0,
`not_helpful` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,664 WARNING database DDL Query made to DB:
create table `tabWebsite Theme Ignore App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`app` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,742 WARNING database DDL Query made to DB:
create table `tabWebsite Slideshow Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`image` text,
`heading` varchar(140),
`description` text,
`url` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,815 WARNING database DDL Query made to DB:
create table `tabCompany History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`year` varchar(140),
`highlight` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,893 WARNING database DDL Query made to DB:
create table `tabWebsite Route Meta` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:52,983 WARNING database DDL Query made to DB:
create table `tabBlog Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`published` int(1) not null default 1,
`title` varchar(140),
`description` text,
`route` varchar(140) unique,
`preview_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,064 WARNING database DDL Query made to DB:
create table `tabColor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,164 WARNING database DDL Query made to DB:
create table `tabWeb Page View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`path` varchar(140),
`referrer` varchar(140),
`browser` varchar(140),
`browser_version` varchar(140),
`is_unique` varchar(140),
`time_zone` varchar(140),
`user_agent` varchar(140),
`source` varchar(140),
`campaign` varchar(140),
`medium` varchar(140),
`visitor_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `path`(`path`),
index `visitor_id`(`visitor_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,242 WARNING database DDL Query made to DB:
create table `tabPersonal Data Deletion Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`status` varchar(140),
`partial` int(1) not null default 0,
`fields` text,
`filtered_by` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,316 WARNING database DDL Query made to DB:
create sequence if not exists web_form_list_column_id_seq nocache nocycle
2025-07-09 18:57:53,332 WARNING database DDL Query made to DB:
create table `tabWeb Form List Column` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,462 WARNING database DDL Query made to DB:
create table `tabBlog Post` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`blog_category` varchar(140),
`blogger` varchar(140),
`route` varchar(140) unique,
`read_time` int(11) not null default 0,
`published_on` date,
`published` int(1) not null default 0,
`featured` int(1) not null default 0,
`hide_cta` int(1) not null default 0,
`enable_email_notification` int(1) not null default 1,
`disable_comments` int(1) not null default 0,
`disable_likes` int(1) not null default 0,
`blog_intro` text,
`content_type` varchar(140) default 'Markdown',
`content` longtext,
`content_md` longtext,
`content_html` longtext,
`email_sent` int(1) not null default 0,
`meta_title` varchar(60),
`meta_description` text,
`meta_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,551 WARNING database DDL Query made to DB:
create table `tabHelp Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`category_description` text,
`published` int(1) not null default 0,
`help_articles` int(11) not null default 0,
`route` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,627 WARNING database DDL Query made to DB:
create table `tabWebsite Route Redirect` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source` text,
`target` text,
`redirect_http_status` varchar(140) default '301',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,706 WARNING database DDL Query made to DB:
create table `tabPersonal Data Download Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`user_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,807 WARNING database DDL Query made to DB:
create table `tabWeb Page Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`web_template` varchar(140),
`web_template_values` longtext,
`css_class` text,
`section_id` varchar(140),
`add_container` int(1) not null default 1,
`add_top_padding` int(1) not null default 1,
`add_bottom_padding` int(1) not null default 1,
`add_border_at_top` int(1) not null default 0,
`add_border_at_bottom` int(1) not null default 0,
`add_shade` int(1) not null default 0,
`hide_block` int(1) not null default 0,
`add_background_image` int(1) not null default 0,
`background_image` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,882 WARNING database DDL Query made to DB:
create table `tabDiscussion Topic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:53,970 WARNING database DDL Query made to DB:
create table `tabWebsite Meta Tag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:54,047 WARNING database DDL Query made to DB:
create table `tabWebsite Sidebar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140),
`group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:54,315 WARNING database DDL Query made to DB:
create table `tabBlogger` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`short_name` varchar(140) unique,
`full_name` varchar(140),
`user` varchar(140),
`bio` text,
`avatar` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:54,398 WARNING database DDL Query made to DB:
create table `tabWeb Template Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`fieldtype` varchar(140) default 'Data',
`reqd` int(1) not null default 0,
`options` text,
`default` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 18:57:54,476 WARNING database DDL Query made to DB:
create table `tabSocial Link Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`social_link_type` varchar(140),
`color` varchar(140),
`background_color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
