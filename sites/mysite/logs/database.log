2025-07-09 19:10:56,173 WARNING database DDL Query made to DB:
create table `tabMultiSelect Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:56,319 WARNING database DDL Query made to DB:
create table `tabMeter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_number` varchar(140) unique,
`meter_type` varchar(140),
`initial_reading` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:56,462 WARNING database DDL Query made to DB:
create table `tabUnit Assets` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`maintenable` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:56,598 WARNING database DDL Query made to DB:
create table `tabPaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`color_code` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:56,744 WARNING database DDL Query made to DB:
create table `tabUnit Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:56,987 WARNING database DDL Query made to DB:
create table `tabLease` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`property` varchar(140),
`lease_date` date,
`lease_status` varchar(140),
`signed_agreement_received` int(1) not null default 0,
`stamp_duty_paid_by_tenant` int(1) not null default 0,
`property_owner` varchar(140),
`lease_customer` varchar(140),
`customer` varchar(140),
`property_user` varchar(140),
`start_date` date,
`skip_end_date` int(1) not null default 0,
`end_date` date,
`frequency` varchar(140),
`days_to_invoice_in_advance` int(11) not null default 0,
`notice_period` int(11) not null default 0,
`fit_out_period` int(11) not null default 0,
`security_deposit_currency` varchar(140) default 'USD',
`security_deposit` decimal(21,9) not null default 0,
`security_status` varchar(140),
`security_received_payment` varchar(140),
`security_returned_reference` varchar(140),
`late_payment_interest_percentage` decimal(21,9) not null default 0,
`wtax_paid_by` varchar(140),
`witness_2` varchar(140),
`witness_1` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:57,194 WARNING database DDL Query made to DB:
create table `tabOutsourcing Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`outsourcing_category` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:57,344 WARNING database DDL Query made to DB:
create table `tabExit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lease` varchar(140),
`exit_type` varchar(140),
`premature_expiry_date` date,
`exit_details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:57,497 WARNING database DDL Query made to DB:
create table `tabFlooring` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`flooring_type` varchar(140) unique,
`description` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:57,642 WARNING database DDL Query made to DB:
create table `tabProperty Meter Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_number` varchar(140),
`meter_type` varchar(140),
`installation_date` date,
`initial_meter_reading` decimal(21,9) not null default 0,
`invoice_customer` varchar(140),
`status` varchar(140),
`deactivation_date` date,
`property_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:57,791 WARNING database DDL Query made to DB:
create table `tabTool Item Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tool_item_set` varchar(140),
`set_name` varchar(140),
`staff_type` varchar(140),
`taken_by` varchar(140),
`reason_for_tool_item_taken` varchar(140),
`datetime_taken` datetime(6),
`returned` int(1) not null default 0,
`return_date_and_time` datetime(6),
`other_remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:57,942 WARNING database DDL Query made to DB:
create table `tabKey Set Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_set` varchar(140),
`set_name` varchar(140),
`staff_type` varchar(140),
`taken_by` varchar(140),
`reason_for_key_taken` varchar(140),
`datetime_taken` datetime(6),
`returned` int(1) not null default 0,
`return_date_and_time` datetime(6),
`other_remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,076 WARNING database DDL Query made to DB:
create table `tabOutsourcing Attendance Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`person_name` varchar(140),
`position` varchar(140),
`status` varchar(140),
`remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,216 WARNING database DDL Query made to DB:
create table `tabMeter Reading Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`meter_number` varchar(140),
`previous_meter_reading` decimal(21,9) not null default 0,
`current_meter_reading` decimal(21,9) not null default 0,
`reading_difference` decimal(21,9) not null default 0,
`previous_reading_date` date,
`do_not_create_invoice` int(1) not null default 0,
`invoice_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,344 WARNING database DDL Query made to DB:
create table `tabGuard Shift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,477 WARNING database DDL Query made to DB:
create table `tabOutsourcing Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_date` date,
`shift_name` varchar(140),
`outsourcing_category_name` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,638 WARNING database DDL Query made to DB:
create table `tabTool Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tool_item_number` varchar(140) unique,
`status_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,786 WARNING database DDL Query made to DB:
create table `tabKey Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`no_of_keys` int(11) not null default 0,
`shelf_no` varchar(140),
`location_no` varchar(140),
`status` varchar(140),
`duplicate_of` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `set_name`(`set_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:58,936 WARNING database DDL Query made to DB:
create table `tabIssue Materials Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`quantity` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_pos` int(1) not null default 0,
`material_status` varchar(140),
`material_request` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:59,076 WARNING database DDL Query made to DB:
create table `tabGuard Shift` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:59,415 WARNING database DDL Query made to DB:
create table `tabIssue Materials Billed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`quantity` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_pos` int(1) not null default 0,
`material_status` varchar(140),
`invoiced` int(1) not null default 0,
`sales_invoice` varchar(140),
`stock_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:10:59,546 WARNING database DDL Query made to DB:
create table `tabOutsourcing Shift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:05,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `security_account_code` varchar(140), ADD COLUMN `default_tax_account_head` varchar(140), ADD COLUMN `default_tax_template` varchar(140), ADD COLUMN `default_maintenance_tax_template` varchar(140)
2025-07-09 19:11:05,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-07-09 19:11:05,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `reading_required` int(1) not null default 0
2025-07-09 19:11:05,495 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-07-09 19:11:05,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` ADD COLUMN `mateiral_request` varchar(140)
2025-07-09 19:11:05,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-09 19:11:05,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `lease` varchar(140), ADD COLUMN `lease_item` varchar(140), ADD COLUMN `job_card` varchar(140)
2025-07-09 19:11:05,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-07-09 19:11:05,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `property_name` varchar(140), ADD COLUMN `person_in_charge` varchar(140), ADD COLUMN `sub_contractor_contact` varchar(140), ADD COLUMN `person_in_charge_name` varchar(140), ADD COLUMN `sub_contractor_name` varchar(140), ADD COLUMN `defect_found` longtext, ADD COLUMN `customer_feedback` longtext
2025-07-09 19:11:05,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `total_hold_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-07-09 19:11:05,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `cost_center` varchar(140)
2025-07-09 19:11:05,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-07-09 19:11:05,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `sales_invoice` varchar(140)
2025-07-09 19:11:06,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-09 19:11:06,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `material_request` varchar(140)
2025-07-09 19:11:06,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-07-09 19:11:06,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` ADD COLUMN `territory` varchar(140)
2025-07-09 19:11:06,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` MODIFY `builtup_area` decimal(21,9) not null default 0, MODIFY `carpet_area` decimal(21,9) not null default 0, MODIFY `rent` decimal(21,9) not null default 0, MODIFY `security_deposit` decimal(21,9) not null default 0
2025-07-09 19:11:34,493 WARNING database DDL Query made to DB:
create table `tabMembership Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`membership_type` varchar(140) unique,
`amount` decimal(21,9) not null default 0,
`razorpay_plan_id` varchar(140) unique,
`linked_item` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:34,744 WARNING database DDL Query made to DB:
create table `tabChapter Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`introduction` varchar(140),
`website_url` varchar(140),
`enabled` int(1) not null default 1,
`leave_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:34,915 WARNING database DDL Query made to DB:
create table `tabGrant Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_type` varchar(140),
`applicant_name` varchar(140),
`contact_person` varchar(140),
`email` varchar(140),
`status` varchar(140) default 'Open',
`website_url` varchar(140),
`company` varchar(140),
`grant_description` longtext,
`amount` decimal(21,9) not null default 0,
`has_any_past_grant_record` int(1) not null default 0,
`route` varchar(140),
`published` int(1) not null default 0,
`assessment_mark` decimal(21,9) not null default 0,
`note` text,
`assessment_manager` varchar(140),
`email_notification_sent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:35,066 WARNING database DDL Query made to DB:
create table `tabTax Exemption 80G Certificate Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`amount` decimal(21,9) not null default 0,
`invoice_id` varchar(140),
`payment_id` varchar(140),
`membership` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:35,225 WARNING database DDL Query made to DB:
create table `tabVolunteer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`volunteer_name` varchar(140),
`volunteer_type` varchar(140),
`email` varchar(140) unique,
`image` text,
`availability` varchar(140),
`availability_timeslot` varchar(140),
`note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:35,360 WARNING database DDL Query made to DB:
create table `tabVolunteer Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`volunteer_skill` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:35,529 WARNING database DDL Query made to DB:
create table `tabMembership` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`membership_type` varchar(140),
`company` varchar(140),
`membership_status` varchar(140),
`from_date` date,
`to_date` date,
`member_since_date` date,
`paid` int(1) not null default 0,
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`invoice` varchar(140),
`subscription_id` varchar(140),
`payment_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:35,696 WARNING database DDL Query made to DB:
create table `tabDonor Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`donor_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:35,857 WARNING database DDL Query made to DB:
create table `tabDonor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`donor_name` varchar(140),
`donor_type` varchar(140),
`email` varchar(140) unique,
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:36,006 WARNING database DDL Query made to DB:
create table `tabCertification Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name_of_applicant` varchar(140),
`email` varchar(140),
`certification_status` varchar(140),
`paid` int(1) not null default 0,
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:36,149 WARNING database DDL Query made to DB:
create table `tabTraining Session Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`status` varchar(140),
`attendance` varchar(140) default 'Present',
`is_mandatory` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:36,504 WARNING database DDL Query made to DB:
create table `tabDonation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`donor` varchar(140),
`donor_name` varchar(140),
`email` varchar(140),
`company` varchar(140),
`date` date,
`paid` int(1) not null default 0,
`amount` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_id` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:36,718 WARNING database DDL Query made to DB:
create table `tabTraining Session` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`event_name` varchar(140) unique,
`training_program` varchar(140),
`event_status` varchar(140),
`has_certificate` int(1) not null default 0,
`type` varchar(140),
`level` varchar(140),
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`course` varchar(140),
`location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`introduction` longtext,
`member_email` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:36,882 WARNING database DDL Query made to DB:
create table `tabChapter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chapter_head` varchar(140),
`region` varchar(140),
`introduction` longtext,
`meetup_embed_html` longtext,
`address` text,
`route` varchar(140),
`published` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:37,071 WARNING database DDL Query made to DB:
create table `tabMember` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`member_name` varchar(140),
`membership_expiry_date` date,
`membership_type` varchar(140),
`email_id` varchar(140),
`image` text,
`customer` varchar(140),
`customer_name` varchar(140),
`supplier` varchar(140),
`subscription_id` varchar(140),
`customer_id` varchar(140),
`subscription_status` varchar(140),
`subscription_start` date,
`subscription_end` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:37,247 WARNING database DDL Query made to DB:
create table `tabMember Employer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member_employer_name` varchar(140) unique,
`invoice` varchar(140),
`paid` int(1) not null default 0,
`currency` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:37,449 WARNING database DDL Query made to DB:
create table `tabTax Exemption 80G Certificate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`recipient` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`member_email` varchar(140),
`member_pan_number` varchar(140),
`donor` varchar(140),
`donor_name` varchar(140),
`donor_email` varchar(140),
`donor_pan_number` varchar(140),
`date` date,
`fiscal_year` varchar(140),
`company` varchar(140),
`company_address` varchar(140),
`company_address_display` text,
`company_pan_number` varchar(140),
`company_80g_number` varchar(140),
`company_80g_wef` date,
`title` varchar(140),
`total` decimal(21,9) not null default 0,
`donation` varchar(140),
`date_of_donation` date,
`amount` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:37,653 WARNING database DDL Query made to DB:
create table `tabCertified Consultant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name_of_consultant` varchar(140),
`country` varchar(140),
`email` varchar(140),
`phone` varchar(140),
`website_url` varchar(140),
`address` text,
`image` text,
`certification_application` varchar(140),
`from_date` date,
`to_date` date,
`introduction` text,
`details` longtext,
`discuss_id` varchar(140),
`github_id` varchar(140),
`show_in_website` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:37,814 WARNING database DDL Query made to DB:
create table `tabVolunteer Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-09 19:11:40,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabMember` ADD COLUMN `pan_number` varchar(140)
2025-07-09 19:11:40,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabDonor` ADD COLUMN `pan_number` varchar(140)
2025-07-09 19:11:40,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `company_80g_number` varchar(140), ADD COLUMN `with_effect_from` date, ADD COLUMN `pan_details` varchar(140)
2025-07-09 19:11:40,940 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
