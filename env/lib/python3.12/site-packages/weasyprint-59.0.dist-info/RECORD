../../../bin/weasyprint,sha256=6Qkggkl3TVDAPKl0dDQZIBh3o8cLpnSmLBd1ywWWiNY,243
weasyprint-59.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
weasyprint-59.0.dist-info/LICENSE,sha256=v9FOzPphAFdUYOaFVWsYM5nUvTNZBOPJUhsBFtIcVNo,1534
weasyprint-59.0.dist-info/METADATA,sha256=kaFhbLUaqywlrwEueDmG-eTabFhz_Asxs1Kj7iC3cDY,3745
weasyprint-59.0.dist-info/RECORD,,
weasyprint-59.0.dist-info/WHEEL,sha256=rSgq_JpHF9fHR1lx53qwg_1-2LypZE_qmcuXbVUq948,81
weasyprint-59.0.dist-info/entry_points.txt,sha256=wgDp3XXzFywdYgI5vUWMp1zAwx1sZXXH0FTUQbFOq6A,55
weasyprint/__init__.py,sha256=1Nn1SuXg6-suuYhPhD1Bs-KfmLB_NpTvOFAWlMfIkSE,15640
weasyprint/__main__.py,sha256=SFNNDBikgI7u7qhAJZ9-ZWP7BIxgroF5DJDWSmeuz5k,7555
weasyprint/__pycache__/__init__.cpython-312.pyc,,
weasyprint/__pycache__/__main__.cpython-312.pyc,,
weasyprint/__pycache__/anchors.cpython-312.pyc,,
weasyprint/__pycache__/document.cpython-312.pyc,,
weasyprint/__pycache__/draw.cpython-312.pyc,,
weasyprint/__pycache__/html.cpython-312.pyc,,
weasyprint/__pycache__/images.cpython-312.pyc,,
weasyprint/__pycache__/logger.cpython-312.pyc,,
weasyprint/__pycache__/matrix.cpython-312.pyc,,
weasyprint/__pycache__/stacking.cpython-312.pyc,,
weasyprint/__pycache__/urls.cpython-312.pyc,,
weasyprint/anchors.py,sha256=aZBjKZTDAdt369hIvXZBai6Mmp7A1-4ObspAHV242Mw,5988
weasyprint/css/__init__.py,sha256=l5i3SIl_yTcDYG9qx3iEmkZ1ai4K2ndA12DnW_pNXM4,49314
weasyprint/css/__pycache__/__init__.cpython-312.pyc,,
weasyprint/css/__pycache__/computed_values.cpython-312.pyc,,
weasyprint/css/__pycache__/counters.cpython-312.pyc,,
weasyprint/css/__pycache__/media_queries.cpython-312.pyc,,
weasyprint/css/__pycache__/properties.cpython-312.pyc,,
weasyprint/css/__pycache__/targets.cpython-312.pyc,,
weasyprint/css/__pycache__/utils.cpython-312.pyc,,
weasyprint/css/computed_values.py,sha256=qVqsS2Oe-IdspQCHwIaBwMW4uHlLAmyTagsfN0StqpQ,27436
weasyprint/css/counters.py,sha256=DHSrGJr2ktpZLCc-JYIiE67ak0TORsKSBKkjju-qwdE,11373
weasyprint/css/html5_ph.css,sha256=u2VD0dJ5nAL3ubnQVd7q9Ii649T8vDKQ539I8h1Fh5o,7407
weasyprint/css/html5_ua.css,sha256=n4LRx50cHZFGHybw-7MML314CPdb52rsj2_Uj-QlorQ,40755
weasyprint/css/html5_ua_form.css,sha256=RDUs3WNYUoXbPZpCs-CIIKBQjj-qoTaGCBPsmodWleM,96
weasyprint/css/media_queries.py,sha256=wHPteZ9Gs2ttuA5kZpMDgRtFHnhYZVwrFXrhKgmR-4g,1072
weasyprint/css/properties.py,sha256=st7jfpNmP5_Zfw-XQn4FEVxgrul8vGm4PY_IMGj8pY4,10186
weasyprint/css/targets.py,sha256=5Ofw1RrmPsfQjDuZ1FCgktspGUai3wJmNa03MbT2sOI,8853
weasyprint/css/tests_ua.css,sha256=Srpfq9Rlp2lakoY3ovGM9dfQtm3zvZJIwRCKgpWU9I4,1630
weasyprint/css/utils.py,sha256=8z9K_oKqvMnmbafsxXf8dxUqIsHoWLUOk2UXR3aU2iM,23770
weasyprint/css/validation/__init__.py,sha256=vCHGjJGx0wZnwg1_-1ukb_B6-DLMo1NYthxld9_oA0o,4042
weasyprint/css/validation/__pycache__/__init__.cpython-312.pyc,,
weasyprint/css/validation/__pycache__/descriptors.cpython-312.pyc,,
weasyprint/css/validation/__pycache__/expanders.cpython-312.pyc,,
weasyprint/css/validation/__pycache__/properties.cpython-312.pyc,,
weasyprint/css/validation/descriptors.py,sha256=FcVW29g_LWOw0uSy499AVG0iQ3kOOZbog5GcPpNv678,10905
weasyprint/css/validation/expanders.py,sha256=TWtwCA3858hdw-SGUSc9k-fAQ3yq2hOJdm2lsqOMLE8,24748
weasyprint/css/validation/properties.py,sha256=EpY_1y6nNjcuTGdz3OxyYvIDEEmIzH_cpRlIKbQQPEY,47315
weasyprint/document.py,sha256=J1D1G4IIHdCeYzjbQ309hG4_yR3kqm0e_-HN6_GreWM,17135
weasyprint/draw.py,sha256=onEvUq4UyUM4XNnsZhYFAwdCgiu-7oEJDAuFLSPyCMM,51370
weasyprint/formatting_structure/__pycache__/boxes.cpython-312.pyc,,
weasyprint/formatting_structure/__pycache__/build.cpython-312.pyc,,
weasyprint/formatting_structure/boxes.py,sha256=UcVO5XDNnnzVKwVPIUbYw98atmiQlb0RakLQZtkK1Vo,24104
weasyprint/formatting_structure/build.py,sha256=FZnYCqDuipbHh7lDh23JbE6d9bz_JL4i8D8JGd3-p2o,61787
weasyprint/html.py,sha256=8r0KbQpaHmNXS53iehgWXmZMCAQNneS7Sw7fRQKYH1k,11492
weasyprint/images.py,sha256=EqBBQqUAz22tFbDzIDzTrCZb_TwWJ9TlYMI0euFjshU,34099
weasyprint/layout/__init__.py,sha256=wn4OeL1qxTz8wIzKSj_ZiXufWmicyTItDXyt6_uHGg8,15614
weasyprint/layout/__pycache__/__init__.cpython-312.pyc,,
weasyprint/layout/__pycache__/absolute.cpython-312.pyc,,
weasyprint/layout/__pycache__/background.cpython-312.pyc,,
weasyprint/layout/__pycache__/block.cpython-312.pyc,,
weasyprint/layout/__pycache__/column.cpython-312.pyc,,
weasyprint/layout/__pycache__/flex.cpython-312.pyc,,
weasyprint/layout/__pycache__/float.cpython-312.pyc,,
weasyprint/layout/__pycache__/inline.cpython-312.pyc,,
weasyprint/layout/__pycache__/leader.cpython-312.pyc,,
weasyprint/layout/__pycache__/min_max.cpython-312.pyc,,
weasyprint/layout/__pycache__/page.cpython-312.pyc,,
weasyprint/layout/__pycache__/percent.cpython-312.pyc,,
weasyprint/layout/__pycache__/preferred.cpython-312.pyc,,
weasyprint/layout/__pycache__/replaced.cpython-312.pyc,,
weasyprint/layout/__pycache__/table.cpython-312.pyc,,
weasyprint/layout/absolute.py,sha256=a_bgVVodVf2XtEQwqqCrBAUxWz8gwA6ljnqRDBmPp5c,13681
weasyprint/layout/background.py,sha256=5uCQ5Gc5FDw_mA0r35tROrzkz6nS6roK9WJr3JA_mCA,9003
weasyprint/layout/block.py,sha256=Yn9JbkTRXiqW5hCbhuBJePtiuZNFyZeo0vmrAE0lNnw,43976
weasyprint/layout/column.py,sha256=7zAohIO1zYw0CROQY2RJIG8tjVUFcTXMSw9MG_FXF6s,17028
weasyprint/layout/flex.py,sha256=pTZvpcJlsWXIByoLtL8nDUT1_aLOLL96JRO5j-dUqBE,38558
weasyprint/layout/float.py,sha256=X2MxAcl7o0fwW5b7630_eA8d5YvVw-Qbvkzv9Xy8XEE,8756
weasyprint/layout/inline.py,sha256=vR8SVdT3jPXBqE92Tpdf2QHF9Qmf8GrPox-XSj0fDUk,47911
weasyprint/layout/leader.py,sha256=j5jVsA6iF_KGX6T_IExbtTWtnVHnmh-iLIqXUCg5eZw,2816
weasyprint/layout/min_max.py,sha256=JdXJG9ISO_RsfeHua_-3g477a16I-NrnYuwH_tQwq4o,1527
weasyprint/layout/page.py,sha256=HCeQUnhqbZ9MnaYWXZXQ4q3-kCWfW9rerZs3H5Qlvcg,35142
weasyprint/layout/percent.py,sha256=xsymQh4-TQawkWUrIDAaKnw3xO3WSJQZS2GZQFxShjU,5940
weasyprint/layout/preferred.py,sha256=PRr8_53vKXgKJlXeNS17SvdGbR0AQELJhTLuorj6NUQ,29564
weasyprint/layout/replaced.py,sha256=ucAd6VMKIEryjnwK8ciKbUoE2yK29-ggdYlGW3KPDXk,11178
weasyprint/layout/table.py,sha256=nPOx8AhV3dAhx7KlWu6v0aWk4upY6hkunbG0Mae5wlI,40754
weasyprint/logger.py,sha256=YUZFup-osH74gqSYI2F2Nqyt66-ju09Vnym2yha2c9Q,803
weasyprint/matrix.py,sha256=v1BPtyn_-S_4TrAUgzOOR-viUXgdqsABKRndCEprkPc,1909
weasyprint/pdf/__init__.py,sha256=qUFiy0H5yLPIKlGehs68-P6ZLzbennTCoYuYhPoyeAo,10604
weasyprint/pdf/__pycache__/__init__.cpython-312.pyc,,
weasyprint/pdf/__pycache__/anchors.cpython-312.pyc,,
weasyprint/pdf/__pycache__/fonts.cpython-312.pyc,,
weasyprint/pdf/__pycache__/metadata.cpython-312.pyc,,
weasyprint/pdf/__pycache__/pdfa.cpython-312.pyc,,
weasyprint/pdf/__pycache__/pdfua.cpython-312.pyc,,
weasyprint/pdf/__pycache__/stream.cpython-312.pyc,,
weasyprint/pdf/anchors.py,sha256=ztLx3NP0ecKdUh2wRtPQVrG0uzkNRSGE08Zxn5X9dmk,13252
weasyprint/pdf/fonts.py,sha256=mmORMaJBjFZHM94kE3k-W8uJxCw4lyxLGfSSe8hNsLQ,12739
weasyprint/pdf/metadata.py,sha256=7ubiNnn-KkWpJK-vB97A6M9C3XJwmSGFNiNP3Yo5Zv0,3907
weasyprint/pdf/pdfa.py,sha256=VLt0hGDXI8U7IBh5eTCrnTd53EOUdEbLe_Xaiammfko,1684
weasyprint/pdf/pdfua.py,sha256=sdbn688uJGLMUx2CJoxVHxaMKcuJtjP7ITnvQzKXEU8,5456
weasyprint/pdf/sRGB2014.icc,sha256=OEuDLeNBIGZ0O1KnXukGtvufuNngnpNvwsQyI4Fcbgo,3024
weasyprint/pdf/stream.py,sha256=_OGM7GiaDLQLt_5CGvl9iqGYGlUdxoHqrenaV-B80BQ,18357
weasyprint/stacking.py,sha256=-H_RHeYFJV6szQHJkmhlAIFvdoNvjuJUG4zqBAubGt0,5506
weasyprint/svg/__init__.py,sha256=n89TTZa5__4yMZKSnzUwaul200ibgBmYzeQFwYg-dNE,27650
weasyprint/svg/__pycache__/__init__.cpython-312.pyc,,
weasyprint/svg/__pycache__/bounding_box.cpython-312.pyc,,
weasyprint/svg/__pycache__/css.cpython-312.pyc,,
weasyprint/svg/__pycache__/defs.cpython-312.pyc,,
weasyprint/svg/__pycache__/images.cpython-312.pyc,,
weasyprint/svg/__pycache__/path.cpython-312.pyc,,
weasyprint/svg/__pycache__/shapes.cpython-312.pyc,,
weasyprint/svg/__pycache__/text.cpython-312.pyc,,
weasyprint/svg/__pycache__/utils.cpython-312.pyc,,
weasyprint/svg/bounding_box.py,sha256=lW-ZJ9QxrXLxnepY1it4HtNiMXHI32AnO5clesTiuyM,12690
weasyprint/svg/css.py,sha256=WZfnJN47Z9KN2Wt7BBJDjqydam8PzXdHdjJzm-WN2kA,3833
weasyprint/svg/defs.py,sha256=H_oAQApuB220mOLI9DUg-U2CmMCxShcjEH7bLLMgEU0,21088
weasyprint/svg/images.py,sha256=wxXs_MvY7vwg1CgXJZGzSMwcou9k0xDfipIp5VoVjNM,2503
weasyprint/svg/path.py,sha256=Z-T6kbUU3pyHhzVV0JSBgO--XaCGXLsH-cS9iAsITMM,10064
weasyprint/svg/shapes.py,sha256=7q6fCb3rI8LcWhjf9-QPFkXxd5DTq-ESdB4wbTA_acQ,3845
weasyprint/svg/text.py,sha256=d2OnugQ69nMhkV5D3xvc5QQN8jNpxXlzZxx1WRNRUuY,6095
weasyprint/svg/utils.py,sha256=358kqI8mMfEttrzK2V3jKH-pgTBYO1QWPk2F2yTHTVI,6644
weasyprint/text/__pycache__/constants.cpython-312.pyc,,
weasyprint/text/__pycache__/ffi.cpython-312.pyc,,
weasyprint/text/__pycache__/fonts.cpython-312.pyc,,
weasyprint/text/__pycache__/line_break.cpython-312.pyc,,
weasyprint/text/constants.py,sha256=_-basiNjo7DXaSXGh-jtfiiEU3yviVIXSaQnDCX48pE,7498
weasyprint/text/ffi.py,sha256=QGgQ8Ac9eyfCN3iWxDG8s5BX5GSwL30MV20jsOIywYY,15189
weasyprint/text/fonts.py,sha256=m5mxCNqqA0So0he29mjLEqie95m3TpS0jeKfSvbYPes,14918
weasyprint/text/line_break.py,sha256=3QqgxEZnCNtrDe4jD3FbSGGzyZe79KgaO5fcOlrUk_Y,23959
weasyprint/urls.py,sha256=tIuXzUMZXkPK5zRi-ugfLcdAanJI4UCx1vpeGgMEbYU,9636
