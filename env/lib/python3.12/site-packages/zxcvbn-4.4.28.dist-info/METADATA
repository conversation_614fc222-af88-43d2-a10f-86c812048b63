Metadata-Version: 2.4
Name: zxcvbn
Version: 4.4.28
Home-page: https://github.com/dwolfhub/zxcvbn-python
Download-URL: https://github.com/dwolfhub/zxcvbn-python/tarball/v4.4.28
Author: <PERSON>
Author-email: daniel<PERSON>wolf<PERSON>@gmail.com
License: MIT
Keywords: zxcvbn,password,security
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Topic :: Security
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: download-url
Dynamic: home-page
Dynamic: keywords
Dynamic: license

|Build Status|

zxcvbn
======

A realistic password strength estimator.

This is a Python implementation of the library created by the team at Dropbox.
The original library, written for JavaScript, can be found
`here <https://github.com/dropbox/zxcvbn>`__.

While there may be other Python ports available, this one is the most up
to date and is recommended by the original developers of zxcvbn at this
time.


Features
--------
- **Tested in Python versions 2.7, 3.3-3.6**
- Accepts user data to be added to the dictionaries that are tested against (name, birthdate, etc)
- Gives a score to the password, from 0 (terrible) to 4 (great)
- Provides feedback on the password and ways to improve it
- Returns time estimates on how long it would take to guess the password in different situations

Installation
------------

Install the package using pip: ``pip install zxcvbn``

Usage
-----

Pass a password as the first parameter, and a list of user-provided
inputs as the ``user_inputs`` parameter (optional).

.. code:: python

    from zxcvbn import zxcvbn

    results = zxcvbn('JohnSmith123', user_inputs=['John', 'Smith'])

    print(results)

Output:

::

    {
        'password': 'JohnSmith123',
        'score': 2,
        'guesses': 2567800,
        'guesses_log10': 6.409561194521849,
        'calc_time': datetime.timedelta(0, 0, 5204)
        'feedback': {
            'warning': '',
            'suggestions': [
                'Add another word or two. Uncommon words are better.',
                "Capitalization doesn't help very much"
            ]
        },
        'crack_times_display': {
            'offline_fast_hashing_1e10_per_second': 'less than a second'
            'offline_slow_hashing_1e4_per_second': '4 minutes',
            'online_no_throttling_10_per_second': '3 days',
            'online_throttling_100_per_hour': '3 years',
        },
        'crack_times_seconds': {
            'offline_fast_hashing_1e10_per_second': 0.00025678,
            'offline_slow_hashing_1e4_per_second': 256.78
            'online_no_throttling_10_per_second': 256780.0,
            'online_throttling_100_per_hour': 92440800.0,
        },
        'sequence': [{
            'matched_word': 'john',
            'rank': 2,
            'pattern': 'dictionary',
            'reversed': False,
            'token': 'John',
            'l33t': False,
            'uppercase_variations': 2,
            'i': 0,
            'guesses': 50,
            'l33t_variations': 1,
            'dictionary_name': 'male_names',
            'base_guesses': 2,
            'guesses_log10': 1.6989700043360185,
            'j': 3
        }, {
            'matched_word': 'smith123',
            'rank': 12789,
            'pattern': 'dictionary',
            'reversed': False,
            'token': 'Smith123',
            'l33t': False,
            'uppercase_variations': 2,
            'i': 4,
            'guesses': 25578,
            'l33t_variations': 1,
            'dictionary_name': 'passwords',
            'base_guesses': 12789,
            'guesses_log10': 4.407866583030775,
            'j': 11
        }],
    }


Custom Ranked Dictionaries
--------------------------

In order to support more languages or just add password dictionaries of your own, there is a helper function you may use.

.. code:: python

    from zxcvbn.matching import add_frequency_lists

    add_frequency_lists({
        'my_list': ['foo', 'bar'],
        'another_list': ['baz']
    })

These lists will be added to the current ones, but you can also overwrite the current ones if you wish.
The lists you add should be in order of how common the word is used with the most common words appearing first.

CLI
~~~

You an also use zxcvbn from the command line::

    echo 'password' | zxcvbn --user-input <user-input> | jq

You can also execute the zxcvbn module::

    echo 'password' | python -m zxcvbn --user-input <user-input> | jq


Contribute
----------

- Report an Issue: https://github.com/dwolfhub/zxcvbn-python/issues
- Submit a Pull Request: https://github.com/dwolfhub/zxcvbn-python/pulls

License
-------

The project is licensed under the MIT license.


.. |Build Status| image:: https://travis-ci.org/dwolfhub/zxcvbn-python.svg?branch=master
   :target: https://travis-ci.org/dwolfhub/zxcvbn-python
