from collections.abc import Callable
from datetime import datetime

class IfRange:
    etag: str | None
    date: datetime | None
    def __init__(
        self, etag: str | None = None, date: datetime | None = None
    ) -> None: ...
    def to_header(self) -> str: ...

class Range:
    units: str
    ranges: list[tuple[int, int | None]]
    def __init__(self, units: str, ranges: list[tuple[int, int | None]]) -> None: ...
    def range_for_length(self, length: int | None) -> tuple[int, int] | None: ...
    def make_content_range(self, length: int | None) -> ContentRange | None: ...
    def to_header(self) -> str: ...
    def to_content_range_header(self, length: int | None) -> str | None: ...

def _callback_property(name: str) -> property: ...

class ContentRange:
    on_update: Callable[[ContentRange], None] | None
    def __init__(
        self,
        units: str | None,
        start: int | None,
        stop: int | None,
        length: int | None = None,
        on_update: Callable[[ContentRange], None] | None = None,
    ) -> None: ...
    @property
    def units(self) -> str | None: ...
    @units.setter
    def units(self, value: str | None) -> None: ...
    @property
    def start(self) -> int | None: ...
    @start.setter
    def start(self, value: int | None) -> None: ...
    @property
    def stop(self) -> int | None: ...
    @stop.setter
    def stop(self, value: int | None) -> None: ...
    @property
    def length(self) -> int | None: ...
    @length.setter
    def length(self, value: int | None) -> None: ...
    def set(
        self,
        start: int | None,
        stop: int | None,
        length: int | None = None,
        units: str | None = "bytes",
    ) -> None: ...
    def unset(self) -> None: ...
    def to_header(self) -> str: ...
