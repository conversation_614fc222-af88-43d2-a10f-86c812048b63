from collections.abc import Callable
from collections.abc import Iterable
from collections.abc import Mapping

from .mixins import UpdateDictMixin

def csp_property(key: str) -> property: ...

class ContentSecurityPolicy(UpdateDictMixin[str, str], dict[str, str]):
    @property
    def base_uri(self) -> str | None: ...
    @base_uri.setter
    def base_uri(self, value: str | None) -> None: ...
    @base_uri.deleter
    def base_uri(self) -> None: ...
    @property
    def child_src(self) -> str | None: ...
    @child_src.setter
    def child_src(self, value: str | None) -> None: ...
    @child_src.deleter
    def child_src(self) -> None: ...
    @property
    def connect_src(self) -> str | None: ...
    @connect_src.setter
    def connect_src(self, value: str | None) -> None: ...
    @connect_src.deleter
    def connect_src(self) -> None: ...
    @property
    def default_src(self) -> str | None: ...
    @default_src.setter
    def default_src(self, value: str | None) -> None: ...
    @default_src.deleter
    def default_src(self) -> None: ...
    @property
    def font_src(self) -> str | None: ...
    @font_src.setter
    def font_src(self, value: str | None) -> None: ...
    @font_src.deleter
    def font_src(self) -> None: ...
    @property
    def form_action(self) -> str | None: ...
    @form_action.setter
    def form_action(self, value: str | None) -> None: ...
    @form_action.deleter
    def form_action(self) -> None: ...
    @property
    def frame_ancestors(self) -> str | None: ...
    @frame_ancestors.setter
    def frame_ancestors(self, value: str | None) -> None: ...
    @frame_ancestors.deleter
    def frame_ancestors(self) -> None: ...
    @property
    def frame_src(self) -> str | None: ...
    @frame_src.setter
    def frame_src(self, value: str | None) -> None: ...
    @frame_src.deleter
    def frame_src(self) -> None: ...
    @property
    def img_src(self) -> str | None: ...
    @img_src.setter
    def img_src(self, value: str | None) -> None: ...
    @img_src.deleter
    def img_src(self) -> None: ...
    @property
    def manifest_src(self) -> str | None: ...
    @manifest_src.setter
    def manifest_src(self, value: str | None) -> None: ...
    @manifest_src.deleter
    def manifest_src(self) -> None: ...
    @property
    def media_src(self) -> str | None: ...
    @media_src.setter
    def media_src(self, value: str | None) -> None: ...
    @media_src.deleter
    def media_src(self) -> None: ...
    @property
    def navigate_to(self) -> str | None: ...
    @navigate_to.setter
    def navigate_to(self, value: str | None) -> None: ...
    @navigate_to.deleter
    def navigate_to(self) -> None: ...
    @property
    def object_src(self) -> str | None: ...
    @object_src.setter
    def object_src(self, value: str | None) -> None: ...
    @object_src.deleter
    def object_src(self) -> None: ...
    @property
    def prefetch_src(self) -> str | None: ...
    @prefetch_src.setter
    def prefetch_src(self, value: str | None) -> None: ...
    @prefetch_src.deleter
    def prefetch_src(self) -> None: ...
    @property
    def plugin_types(self) -> str | None: ...
    @plugin_types.setter
    def plugin_types(self, value: str | None) -> None: ...
    @plugin_types.deleter
    def plugin_types(self) -> None: ...
    @property
    def report_to(self) -> str | None: ...
    @report_to.setter
    def report_to(self, value: str | None) -> None: ...
    @report_to.deleter
    def report_to(self) -> None: ...
    @property
    def report_uri(self) -> str | None: ...
    @report_uri.setter
    def report_uri(self, value: str | None) -> None: ...
    @report_uri.deleter
    def report_uri(self) -> None: ...
    @property
    def sandbox(self) -> str | None: ...
    @sandbox.setter
    def sandbox(self, value: str | None) -> None: ...
    @sandbox.deleter
    def sandbox(self) -> None: ...
    @property
    def script_src(self) -> str | None: ...
    @script_src.setter
    def script_src(self, value: str | None) -> None: ...
    @script_src.deleter
    def script_src(self) -> None: ...
    @property
    def script_src_attr(self) -> str | None: ...
    @script_src_attr.setter
    def script_src_attr(self, value: str | None) -> None: ...
    @script_src_attr.deleter
    def script_src_attr(self) -> None: ...
    @property
    def script_src_elem(self) -> str | None: ...
    @script_src_elem.setter
    def script_src_elem(self, value: str | None) -> None: ...
    @script_src_elem.deleter
    def script_src_elem(self) -> None: ...
    @property
    def style_src(self) -> str | None: ...
    @style_src.setter
    def style_src(self, value: str | None) -> None: ...
    @style_src.deleter
    def style_src(self) -> None: ...
    @property
    def style_src_attr(self) -> str | None: ...
    @style_src_attr.setter
    def style_src_attr(self, value: str | None) -> None: ...
    @style_src_attr.deleter
    def style_src_attr(self) -> None: ...
    @property
    def style_src_elem(self) -> str | None: ...
    @style_src_elem.setter
    def style_src_elem(self, value: str | None) -> None: ...
    @style_src_elem.deleter
    def style_src_elem(self) -> None: ...
    @property
    def worker_src(self) -> str | None: ...
    @worker_src.setter
    def worker_src(self, value: str | None) -> None: ...
    @worker_src.deleter
    def worker_src(self) -> None: ...
    provided: bool
    def __init__(
        self,
        values: Mapping[str, str] | Iterable[tuple[str, str]] = (),
        on_update: Callable[[ContentSecurityPolicy], None] | None = None,
    ) -> None: ...
    def _get_value(self, key: str) -> str | None: ...
    def _set_value(self, key: str, value: str) -> None: ...
    def _del_value(self, key: str) -> None: ...
    def to_header(self) -> str: ...
