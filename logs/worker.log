2025-07-09 19:15:41,933 Worker rq:worker:******************************** started with PID 16387, version 1.15.1
2025-07-09 19:15:41,933 Subscribing to channel rq:pubsub:********************************
2025-07-09 19:15:41,935 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-09 19:15:41,936 Cleaning registries for queue: home-imetumba-dev-mybench:short
2025-07-09 19:15:41,938 Cleaning registries for queue: home-imetumba-dev-mybench:default
2025-07-09 19:15:41,941 Cleaning registries for queue: home-imetumba-dev-mybench:long
2025-07-09 19:17:40,225 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.create_contact', kwargs={'user': <User: <EMAIL>>, 'ignore_mandatory': True}, method='frappe.core.doctype.user.user.create_contact', site='mysite', user='Administrator') (mysite::4e7bf2c2-976c-49d3-a624-0fea26d87457)
2025-07-09 19:17:41,665 home-imetumba-dev-mybench:default: Job OK (mysite::4e7bf2c2-976c-49d3-a624-0fea26d87457)
2025-07-09 19:17:41,666 Result is kept for 600 seconds
2025-07-09 19:17:41,678 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.update_gravatar', kwargs={'name': '<EMAIL>'}, method='frappe.core.doctype.user.user.update_gravatar', site='mysite', user='Administrator') (mysite::4849de10-08af-43fe-916b-6c058a292353)
2025-07-09 19:17:41,988 home-imetumba-dev-mybench:default: Job OK (mysite::4849de10-08af-43fe-916b-6c058a292353)
2025-07-09 19:17:41,988 Result is kept for 600 seconds
2025-07-09 19:17:43,997 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='erpnext.setup.demo.setup_demo_data', kwargs={}, method=<function setup_demo_data at 0x7cf9922cd440>, site='mysite', user='<EMAIL>') (mysite::fca7127f-7ce2-4305-b7a7-7b00c349d311)
2025-07-09 19:18:06,459 home-imetumba-dev-mybench:default: Job OK (mysite::fca7127f-7ce2-4305-b7a7-7b00c349d311)
2025-07-09 19:18:06,460 Result is kept for 600 seconds
2025-07-09 19:19:58,177 Worker ******************************** [PID 16387]: warm shut down requested
2025-07-09 19:19:58,179 Unsubscribing from channel rq:pubsub:********************************
2025-07-09 21:48:36,697 Worker rq:worker:******************************** started with PID 5137, version 1.15.1
2025-07-09 21:48:36,698 Subscribing to channel rq:pubsub:********************************
2025-07-09 21:48:36,699 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-09 21:48:36,700 Cleaning registries for queue: home-imetumba-dev-mybench:short
2025-07-09 21:48:36,702 Cleaning registries for queue: home-imetumba-dev-mybench:default
2025-07-09 21:48:36,703 Cleaning registries for queue: home-imetumba-dev-mybench:long
2025-07-09 21:48:55,822 Worker ******************************** [PID 5137]: warm shut down requested
2025-07-09 21:48:55,823 Unsubscribing from channel rq:pubsub:********************************
2025-07-09 21:49:55,606 Worker rq:worker:******************************** started with PID 6587, version 1.15.1
2025-07-09 21:49:55,606 Subscribing to channel rq:pubsub:********************************
2025-07-09 21:49:55,608 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-09 21:50:56,252 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 21:50:57,155 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 21:50:57,155 Result is kept for 600 seconds
2025-07-09 21:50:57,164 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 21:50:57,486 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 21:50:57,486 Result is kept for 600 seconds
2025-07-09 21:50:57,494 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.process_download_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 21:50:57,955 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 21:50:57,955 Result is kept for 600 seconds
2025-07-09 21:50:57,968 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_log.bom_update_log.r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 21:50:58,342 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 21:50:58,343 Result is kept for 600 seconds
2025-07-09 21:50:58,357 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 21:50:58,744 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 21:50:58,744 Result is kept for 600 seconds
2025-07-09 21:50:58,755 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 21:50:59,276 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 21:50:59,276 Result is kept for 600 seconds
2025-07-09 21:50:59,291 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 21:50:59,562 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 21:50:59,562 Result is kept for 600 seconds
2025-07-09 21:50:59,571 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 21:50:59,958 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 21:50:59,958 Result is kept for 600 seconds
2025-07-09 21:50:59,967 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 21:51:00,469 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 21:51:00,469 Result is kept for 600 seconds
2025-07-09 21:51:00,478 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 21:51:00,694 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 21:51:00,694 Result is kept for 600 seconds
2025-07-09 21:51:00,703 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.sync_all_stanbank_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 21:51:01,078 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 21:51:01,078 Result is kept for 600 seconds
2025-07-09 21:51:01,087 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 21:51:01,345 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 21:51:01,346 Result is kept for 600 seconds
2025-07-09 21:51:01,354 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 21:51:01,925 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 21:51:01,925 Result is kept for 600 seconds
2025-07-09 21:51:01,936 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.items_revaluation.process_incorrect_balanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 21:51:02,284 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 21:51:02,285 Result is kept for 600 seconds
2025-07-09 21:51:02,295 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 21:51:02,521 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 21:51:02,521 Result is kept for 600 seconds
2025-07-09 21:51:59,825 Worker ******************************** [PID 6587]: warm shut down requested
2025-07-09 21:51:59,831 Unsubscribing from channel rq:pubsub:********************************
2025-07-09 21:52:07,530 Worker rq:worker:******************************** started with PID 8177, version 1.15.1
2025-07-09 21:52:07,531 Subscribing to channel rq:pubsub:********************************
2025-07-09 21:52:07,547 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-09 21:53:08,145 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 21:53:08,602 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 21:53:08,603 Result is kept for 600 seconds
2025-07-09 21:53:08,614 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 21:53:09,031 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 21:53:09,032 Result is kept for 600 seconds
2025-07-09 21:53:09,044 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 21:53:09,505 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 21:53:09,505 Result is kept for 600 seconds
2025-07-09 21:53:09,514 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 21:53:09,800 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 21:53:09,801 Result is kept for 600 seconds
2025-07-09 21:53:09,842 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 21:53:10,449 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 21:53:10,449 Result is kept for 600 seconds
2025-07-09 21:56:08,950 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 21:56:09,311 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 21:56:09,311 Result is kept for 600 seconds
2025-07-09 21:56:09,333 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 21:56:09,888 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 21:56:09,888 Result is kept for 600 seconds
2025-07-09 21:56:09,899 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 21:56:10,399 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 21:56:10,400 Result is kept for 600 seconds
2025-07-09 21:56:10,411 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 21:56:10,893 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 21:56:10,894 Result is kept for 600 seconds
2025-07-09 21:56:10,907 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 21:56:11,171 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 21:56:11,171 Result is kept for 600 seconds
2025-07-09 21:59:01,472 Worker ******************************** [PID 8177]: warm shut down requested
2025-07-09 21:59:01,474 Unsubscribing from channel rq:pubsub:********************************
2025-07-09 22:00:06,005 Worker rq:worker:******************************** started with PID 5177, version 1.15.1
2025-07-09 22:00:06,006 Subscribing to channel rq:pubsub:********************************
2025-07-09 22:00:06,009 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-09 22:01:06,898 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.hourly_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.hourly_reminder)
2025-07-09 22:01:08,023 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.hourly_reminder)
2025-07-09 22:01:08,024 Result is kept for 600 seconds
2025-07-09 22:01:08,037 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.daily_work_summary_group.daily_work_summary_g..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails)
2025-07-09 22:01:08,449 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails)
2025-07-09 22:01:08,449 Result is kept for 600 seconds
2025-07-09 22:01:08,459 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 22:01:08,835 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 22:01:08,835 Result is kept for 600 seconds
2025-07-09 22:01:08,848 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:01:09,450 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:01:09,451 Result is kept for 600 seconds
2025-07-09 22:01:09,463 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.sync_all_stanbank_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 22:01:10,537 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 22:01:10,537 Result is kept for 600 seconds
2025-07-09 22:01:10,555 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:01:11,401 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:01:11,401 Result is kept for 600 seconds
2025-07-09 22:01:11,412 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 22:01:11,784 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 22:01:11,784 Result is kept for 600 seconds
2025-07-09 22:01:11,796 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:01:12,884 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:01:12,885 Result is kept for 600 seconds
2025-07-09 22:01:12,910 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:01:13,587 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:01:13,588 Result is kept for 600 seconds
2025-07-09 22:01:13,602 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.erpnext_integrations.doctype.plaid_settings.plaid_set..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization)
2025-07-09 22:01:14,264 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization)
2025-07-09 22:01:14,264 Result is kept for 600 seconds
2025-07-09 22:01:14,278 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.collect_project_stat..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.collect_project_status)
2025-07-09 22:01:14,975 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.collect_project_status)
2025-07-09 22:01:14,976 Result is kept for 600 seconds
2025-07-09 22:01:15,006 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.newsletter.newsletter.send_scheduled_ema..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.newsletter.newsletter.send_scheduled_email)
2025-07-09 22:01:15,646 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.newsletter.newsletter.send_scheduled_email)
2025-07-09 22:01:15,647 Result is kept for 600 seconds
2025-07-09 22:01:15,673 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_log.bom_update_log.r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 22:01:16,381 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 22:01:16,381 Result is kept for 600 seconds
2025-07-09 22:01:16,399 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 22:01:16,796 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 22:01:16,796 Result is kept for 600 seconds
2025-07-09 22:01:16,808 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:01:17,308 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:01:17,309 Result is kept for 600 seconds
2025-07-09 22:01:17,360 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 22:01:18,003 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 22:01:18,003 Result is kept for 600 seconds
2025-07-09 22:01:18,033 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.project_status_updat..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.project_status_update_reminder)
2025-07-09 22:01:18,603 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.project_status_update_reminder)
2025-07-09 22:01:18,604 Result is kept for 600 seconds
2025-07-09 22:01:18,629 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.process_download_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 22:01:19,308 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 22:01:19,308 Result is kept for 600 seconds
2025-07-09 22:01:19,325 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 22:01:19,909 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 22:01:19,919 Result is kept for 600 seconds
2025-07-09 22:01:19,984 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles)
2025-07-09 22:01:20,685 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles)
2025-07-09 22:01:20,686 Result is kept for 600 seconds
2025-07-09 22:01:20,701 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.items_revaluation.process_incorrect_balanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 22:01:21,383 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 22:01:21,383 Result is kept for 600 seconds
2025-07-09 22:01:21,395 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.utilities.doctype.video.video.update_youtube_data'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.utilities.doctype.video.video.update_youtube_data)
2025-07-09 22:01:22,025 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.utilities.doctype.video.video.update_youtube_data)
2025-07-09 22:01:22,025 Result is kept for 600 seconds
2025-07-09 22:01:22,044 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:01:22,362 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:01:22,363 Result is kept for 600 seconds
2025-07-09 22:01:22,375 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.core.doctype.prepared_report.prepared_report.expire_st..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report)
2025-07-09 22:01:22,650 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report)
2025-07-09 22:01:22,650 Result is kept for 600 seconds
2025-07-09 22:01:22,662 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.stock.doctype.repost_item_valuation.repost_item_valua..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries)
2025-07-09 22:01:23,111 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries)
2025-07-09 22:01:23,111 Result is kept for 600 seconds
2025-07-09 22:01:23,124 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.shift_type.shift_type.process_auto_attendance..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts)
2025-07-09 22:01:23,529 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts)
2025-07-09 22:01:23,529 Result is kept for 600 seconds
2025-07-09 22:01:23,541 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assi..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation)
2025-07-09 22:01:24,002 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation)
2025-07-09 22:01:24,003 Result is kept for 600 seconds
2025-07-09 22:01:24,016 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.page.backups.backups.delete_downloadable_backups'..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.page.backups.backups.delete_downloadable_backups)
2025-07-09 22:01:24,340 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.desk.page.backups.backups.delete_downloadable_backups)
2025-07-09 22:01:24,340 Result is kept for 600 seconds
2025-07-09 22:01:24,351 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.form.document_follow.send_hourly_updates'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.form.document_follow.send_hourly_updates)
2025-07-09 22:01:24,677 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.desk.form.document_follow.send_hourly_updates)
2025-07-09 22:01:24,677 Result is kept for 600 seconds
2025-07-09 22:01:24,687 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.user_settings.sync_user_settings'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.user_settings.sync_user_settings)
2025-07-09 22:01:24,970 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.model.utils.user_settings.sync_user_settings)
2025-07-09 22:01:24,971 Result is kept for 600 seconds
2025-07-09 22:01:24,980 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.oauth.delete_oauth2_data'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.oauth.delete_oauth2_data)
2025-07-09 22:01:25,309 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.oauth.delete_oauth2_data)
2025-07-09 22:01:25,310 Result is kept for 600 seconds
2025-07-09 22:01:25,319 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.personal_data_deletion_request.persona..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request)
2025-07-09 22:01:25,681 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request)
2025-07-09 22:01:25,681 Result is kept for 600 seconds
2025-07-09 22:01:25,693 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.web_page.web_page.check_publish_status..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.website.doctype.web_page.web_page.check_publish_status)
2025-07-09 22:01:26,285 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.website.doctype.web_page.web_page.check_publish_status)
2025-07-09 22:01:26,286 Result is kept for 600 seconds
2025-07-09 22:01:26,297 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.utilities.bulk_transaction.retry'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.utilities.bulk_transaction.retry)
2025-07-09 22:01:26,679 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.utilities.bulk_transaction.retry)
2025-07-09 22:01:26,679 Result is kept for 600 seconds
2025-07-09 22:01:26,691 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.twofactor.delete_all_barcodes_for_users'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.twofactor.delete_all_barcodes_for_users)
2025-07-09 22:01:27,037 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.twofactor.delete_all_barcodes_for_users)
2025-07-09 22:01:27,037 Result is kept for 600 seconds
2025-07-09 22:01:27,050 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_che..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin)
2025-07-09 22:01:27,782 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin)
2025-07-09 22:01:27,783 Result is kept for 600 seconds
2025-07-09 22:04:07,871 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:04:08,292 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:04:08,292 Result is kept for 600 seconds
2025-07-09 22:04:08,302 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:04:08,734 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:04:08,734 Result is kept for 600 seconds
2025-07-09 22:04:08,744 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:04:08,968 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:04:08,968 Result is kept for 600 seconds
2025-07-09 22:04:08,976 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:04:09,239 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:04:09,240 Result is kept for 600 seconds
2025-07-09 22:04:09,248 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:04:09,650 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:04:09,650 Result is kept for 600 seconds
2025-07-09 22:08:09,013 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:08:09,430 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:08:09,431 Result is kept for 600 seconds
2025-07-09 22:08:09,440 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:08:09,853 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:08:09,853 Result is kept for 600 seconds
2025-07-09 22:08:09,863 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:08:10,286 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:08:10,286 Result is kept for 600 seconds
2025-07-09 22:08:10,295 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:08:10,529 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:08:10,530 Result is kept for 600 seconds
2025-07-09 22:08:10,539 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:08:10,774 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:08:10,775 Result is kept for 600 seconds
2025-07-09 22:10:09,476 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:10:09,793 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:10:09,794 Result is kept for 600 seconds
2025-07-09 22:10:09,802 Cleaning registries for queue: home-imetumba-dev-mybench:short
2025-07-09 22:10:09,803 Cleaning registries for queue: home-imetumba-dev-mybench:default
2025-07-09 22:10:09,804 Cleaning registries for queue: home-imetumba-dev-mybench:long
2025-07-09 22:11:04,085 Worker ******************************** [PID 5177]: warm shut down requested
2025-07-09 22:11:04,087 Unsubscribing from channel rq:pubsub:********************************
2025-07-09 22:11:32,905 Worker rq:worker:******************************** started with PID 12128, version 1.15.1
2025-07-09 22:11:32,905 Subscribing to channel rq:pubsub:********************************
2025-07-09 22:11:32,910 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-09 22:12:33,822 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:12:34,705 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:12:34,705 Result is kept for 600 seconds
2025-07-09 22:12:34,720 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:12:34,980 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:12:34,980 Result is kept for 600 seconds
2025-07-09 22:12:34,989 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:12:35,509 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:12:35,510 Result is kept for 600 seconds
2025-07-09 22:12:35,520 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:12:36,176 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:12:36,176 Result is kept for 600 seconds
2025-07-09 22:12:36,191 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:12:36,451 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:12:36,452 Result is kept for 600 seconds
2025-07-09 22:15:34,482 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.items_revaluation.process_incorrect_balanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 22:15:35,161 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 22:15:35,161 Result is kept for 600 seconds
2025-07-09 22:15:35,170 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 22:15:35,503 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 22:15:35,503 Result is kept for 600 seconds
2025-07-09 22:15:35,512 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.sync_all_stanbank_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 22:15:35,905 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 22:15:35,906 Result is kept for 600 seconds
2025-07-09 22:15:35,922 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_log.bom_update_log.r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 22:15:36,235 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 22:15:36,235 Result is kept for 600 seconds
2025-07-09 22:15:36,244 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 22:15:36,547 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 22:15:36,547 Result is kept for 600 seconds
2025-07-09 22:15:36,556 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 22:15:36,814 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 22:15:36,814 Result is kept for 600 seconds
2025-07-09 22:15:36,823 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 22:15:37,069 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 22:15:37,069 Result is kept for 600 seconds
2025-07-09 22:15:37,078 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.process_download_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 22:15:37,500 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 22:15:37,500 Result is kept for 600 seconds
2025-07-09 22:15:37,510 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 22:15:37,763 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 22:15:37,763 Result is kept for 600 seconds
2025-07-09 22:16:34,753 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:16:35,163 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:16:35,163 Result is kept for 600 seconds
2025-07-09 22:16:35,173 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:16:35,573 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:16:35,573 Result is kept for 600 seconds
2025-07-09 22:16:35,583 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:16:36,013 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:16:36,014 Result is kept for 600 seconds
2025-07-09 22:16:36,030 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:16:36,302 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:16:36,303 Result is kept for 600 seconds
2025-07-09 22:16:36,316 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:16:36,656 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:16:36,656 Result is kept for 600 seconds
2025-07-09 22:20:35,839 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:20:36,286 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:20:36,287 Result is kept for 600 seconds
2025-07-09 22:20:36,297 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:20:36,537 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:20:36,538 Result is kept for 600 seconds
2025-07-09 22:20:36,547 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:20:36,974 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:20:36,974 Result is kept for 600 seconds
2025-07-09 22:20:36,985 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:20:37,235 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:20:37,236 Result is kept for 600 seconds
2025-07-09 22:20:37,245 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:20:37,658 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:20:37,659 Result is kept for 600 seconds
2025-07-09 22:20:37,669 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:20:37,985 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:20:37,985 Result is kept for 600 seconds
2025-07-09 22:24:36,992 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:24:37,400 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:24:37,400 Result is kept for 600 seconds
2025-07-09 22:24:37,412 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:24:37,641 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:24:37,641 Result is kept for 600 seconds
2025-07-09 22:24:37,650 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:24:38,060 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:24:38,061 Result is kept for 600 seconds
2025-07-09 22:24:38,071 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:24:38,480 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:24:38,480 Result is kept for 600 seconds
2025-07-09 22:24:38,493 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:24:38,735 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:24:38,735 Result is kept for 600 seconds
2025-07-09 22:28:37,958 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:28:38,373 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:28:38,373 Result is kept for 600 seconds
2025-07-09 22:28:38,383 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:28:38,615 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:28:38,615 Result is kept for 600 seconds
2025-07-09 22:28:38,624 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:28:39,035 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:28:39,035 Result is kept for 600 seconds
2025-07-09 22:28:39,046 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:28:39,449 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:28:39,450 Result is kept for 600 seconds
2025-07-09 22:28:39,460 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:28:39,694 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:28:39,694 Result is kept for 600 seconds
2025-07-09 22:30:38,471 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.sync_all_stanbank_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 22:30:38,826 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-09 22:30:38,826 Result is kept for 600 seconds
2025-07-09 22:30:38,835 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.process_download_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 22:30:39,293 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-09 22:30:39,293 Result is kept for 600 seconds
2025-07-09 22:30:39,304 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 22:30:39,569 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-09 22:30:39,569 Result is kept for 600 seconds
2025-07-09 22:30:39,578 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 22:30:39,869 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-09 22:30:39,869 Result is kept for 600 seconds
2025-07-09 22:30:39,878 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:30:40,172 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:30:40,172 Result is kept for 600 seconds
2025-07-09 22:30:40,182 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_log.bom_update_log.r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 22:30:40,459 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-09 22:30:40,459 Result is kept for 600 seconds
2025-07-09 22:30:40,467 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 22:30:40,745 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-09 22:30:40,746 Result is kept for 600 seconds
2025-07-09 22:30:40,755 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.items_revaluation.process_incorrect_balanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 22:30:41,040 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-09 22:30:41,040 Result is kept for 600 seconds
2025-07-09 22:30:41,050 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_doc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs)
2025-07-09 22:30:41,557 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs)
2025-07-09 22:30:41,557 Result is kept for 600 seconds
2025-07-09 22:30:41,566 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.utilities.doctype.video.video.update_youtube_data'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.utilities.doctype.video.video.update_youtube_data)
2025-07-09 22:30:42,028 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.utilities.doctype.video.video.update_youtube_data)
2025-07-09 22:30:42,028 Result is kept for 600 seconds
2025-07-09 22:30:42,044 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 22:30:42,314 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-09 22:30:42,314 Result is kept for 600 seconds
2025-07-09 22:30:42,325 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 22:30:42,615 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-09 22:30:42,615 Result is kept for 600 seconds
2025-07-09 22:32:39,035 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:32:39,429 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:32:39,429 Result is kept for 600 seconds
2025-07-09 22:32:39,438 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:32:39,664 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:32:39,664 Result is kept for 600 seconds
2025-07-09 22:32:39,673 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:32:39,894 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:32:39,895 Result is kept for 600 seconds
2025-07-09 22:32:39,903 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:32:40,307 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:32:40,308 Result is kept for 600 seconds
2025-07-09 22:32:40,319 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:32:40,752 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:32:40,752 Result is kept for 600 seconds
2025-07-09 22:36:39,965 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:36:40,223 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:36:40,223 Result is kept for 600 seconds
2025-07-09 22:36:40,234 Cleaning registries for queue: home-imetumba-dev-mybench:short
2025-07-09 22:36:40,236 Cleaning registries for queue: home-imetumba-dev-mybench:default
2025-07-09 22:36:40,237 Cleaning registries for queue: home-imetumba-dev-mybench:long
2025-07-09 22:36:40,240 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:36:40,686 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:36:40,686 Result is kept for 600 seconds
2025-07-09 22:36:40,699 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:36:41,143 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:36:41,144 Result is kept for 600 seconds
2025-07-09 22:36:41,156 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:36:41,599 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:36:41,599 Result is kept for 600 seconds
2025-07-09 22:36:41,608 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:36:41,860 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:36:41,861 Result is kept for 600 seconds
2025-07-09 22:40:40,998 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:40:41,260 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-09 22:40:41,260 Result is kept for 600 seconds
2025-07-09 22:40:41,272 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:40:41,693 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-09 22:40:41,693 Result is kept for 600 seconds
2025-07-09 22:40:41,704 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:40:42,188 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-09 22:40:42,188 Result is kept for 600 seconds
2025-07-09 22:40:42,198 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:40:42,439 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-09 22:40:42,439 Result is kept for 600 seconds
2025-07-09 22:40:42,447 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:40:42,775 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-09 22:40:42,775 Result is kept for 600 seconds
2025-07-09 22:40:42,786 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:40:43,188 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-09 22:40:43,188 Result is kept for 600 seconds
2025-07-09 22:41:22,635 Worker ******************************** [PID 12128]: warm shut down requested
2025-07-09 22:41:22,638 Unsubscribing from channel rq:pubsub:********************************
2025-07-10 08:29:31,026 Worker rq:worker:******************************** started with PID 6144, version 1.15.1
2025-07-10 08:29:31,027 Subscribing to channel rq:pubsub:********************************
2025-07-10 08:29:31,029 *** Listening on home-imetumba-dev-mybench:short, home-imetumba-dev-mybench:default, home-imetumba-dev-mybench:long...
2025-07-10 08:29:31,030 Cleaning registries for queue: home-imetumba-dev-mybench:short
2025-07-10 08:29:31,032 Cleaning registries for queue: home-imetumba-dev-mybench:default
2025-07-10 08:29:31,034 Cleaning registries for queue: home-imetumba-dev-mybench:long
2025-07-10 08:30:31,897 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.crm.doctype.opportunity.opportunity.auto_close_opport..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity)
2025-07-10 08:30:32,853 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity)
2025-07-10 08:30:32,853 Result is kept for 600 seconds
2025-07-10 08:30:32,864 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.crm.doctype.email_campaign.email_campaign.set_email_c..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status)
2025-07-10 08:30:33,353 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status)
2025-07-10 08:30:33,353 Result is kept for 600 seconds
2025-07-10 08:30:33,363 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.doctype.event.event.send_event_digest'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.doctype.event.event.send_event_digest)
2025-07-10 08:30:33,646 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.desk.doctype.event.event.send_event_digest)
2025-07-10 08:30:33,646 Result is kept for 600 seconds
2025-07-10 08:30:33,655 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.bank_api.reconciliation'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.bank_api.reconciliation)
2025-07-10 08:30:34,188 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.bank_api.reconciliation)
2025-07-10 08:30:34,188 Result is kept for 600 seconds
2025-07-10 08:30:34,197 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-10 08:30:34,528 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-10 08:30:34,528 Result is kept for 600 seconds
2025-07-10 08:30:34,536 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.newsletter.newsletter.send_scheduled_ema..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.newsletter.newsletter.send_scheduled_email)
2025-07-10 08:30:34,919 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.newsletter.newsletter.send_scheduled_email)
2025-07-10 08:30:34,919 Result is kept for 600 seconds
2025-07-10 08:30:34,928 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-10 08:30:35,172 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-10 08:30:35,172 Result is kept for 600 seconds
2025-07-10 08:30:35,180 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'propms.auto_custom.statusChangeAfterLeaseExpire'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::propms.auto_custom.statusChangeAfterLeaseExpire)
2025-07-10 08:30:35,513 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::propms.auto_custom.statusChangeAfterLeaseExpire)
2025-07-10 08:30:35,514 Result is kept for 600 seconds
2025-07-10 08:30:35,521 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.notification.notification.trigger_daily_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.notification.notification.trigger_daily_alerts)
2025-07-10 08:30:35,900 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.notification.notification.trigger_daily_alerts)
2025-07-10 08:30:35,900 Result is kept for 600 seconds
2025-07-10 08:30:35,908 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.quality_management.doctype.quality_review.quality_rev..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.quality_management.doctype.quality_review.quality_review.review)
2025-07-10 08:30:36,247 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.quality_management.doctype.quality_review.quality_review.review)
2025-07-10 08:30:36,247 Result is kept for 600 seconds
2025-07-10 08:30:36,255 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:30:36,724 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:30:36,724 Result is kept for 600 seconds
2025-07-10 08:30:36,732 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.erpnext_integrations.doctype.plaid_settings.plaid_set..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization)
2025-07-10 08:30:37,046 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization)
2025-07-10 08:30:37,046 Result is kept for 600 seconds
2025-07-10 08:30:37,054 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.crm.doctype.contract.contract.update_status_for_contr..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.crm.doctype.contract.contract.update_status_for_contracts)
2025-07-10 08:30:37,346 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.crm.doctype.contract.contract.update_status_for_contracts)
2025-07-10 08:30:37,346 Result is kept for 600 seconds
2025-07-10 08:30:37,354 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.stock.reorder_item.reorder_item'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.stock.reorder_item.reorder_item)
2025-07-10 08:30:37,665 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.stock.reorder_item.reorder_item)
2025-07-10 08:30:37,666 Result is kept for 600 seconds
2025-07-10 08:30:37,674 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:30:38,322 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:30:38,322 Result is kept for 600 seconds
2025-07-10 08:30:38,331 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-10 08:30:38,568 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-10 08:30:38,569 Result is kept for 600 seconds
2025-07-10 08:30:38,577 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.stock.doctype.serial_no.serial_no.update_maintenance_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status)
2025-07-10 08:30:38,923 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status)
2025-07-10 08:30:38,923 Result is kept for 600 seconds
2025-07-10 08:30:38,932 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'non_profit.non_profit.doctype.membership.membership.set_expir..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::non_profit.non_profit.doctype.membership.membership.set_expired_status)
2025-07-10 08:30:39,349 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::non_profit.non_profit.doctype.membership.membership.set_expired_status)
2025-07-10 08:30:39,349 Result is kept for 600 seconds
2025-07-10 08:30:39,358 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles)
2025-07-10 08:30:39,779 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles)
2025-07-10 08:30:39,779 Result is kept for 600 seconds
2025-07-10 08:30:39,787 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.assets.doctype.asset.asset.make_post_gl_entry'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.assets.doctype.asset.asset.make_post_gl_entry)
2025-07-10 08:30:40,182 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.assets.doctype.asset.asset.make_post_gl_entry)
2025-07-10 08:30:40,182 Result is kept for 600 seconds
2025-07-10 08:30:40,191 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_ale..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts)
2025-07-10 08:30:40,469 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts)
2025-07-10 08:30:40,469 Result is kept for 600 seconds
2025-07-10 08:30:40,477 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.crm.doctype.email_campaign.email_campaign.send_email_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts)
2025-07-10 08:30:40,880 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts)
2025-07-10 08:30:40,881 Result is kept for 600 seconds
2025-07-10 08:30:40,890 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_doc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs)
2025-07-10 08:30:41,291 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs)
2025-07-10 08:30:41,291 Result is kept for 600 seconds
2025-07-10 08:30:41,300 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.material_request.auto_close_material_reque..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.material_request.auto_close_material_request)
2025-07-10 08:30:41,731 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.material_request.auto_close_material_request)
2025-07-10 08:30:41,732 Result is kept for 600 seconds
2025-07-10 08:30:41,741 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.items_revaluation.process_incorrect_balanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-10 08:30:41,991 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-10 08:30:41,991 Result is kept for 600 seconds
2025-07-10 08:30:41,999 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.custom_api.auto_close_dn'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.custom_api.auto_close_dn)
2025-07-10 08:30:42,327 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.custom_api.auto_close_dn)
2025-07-10 08:30:42,327 Result is kept for 600 seconds
2025-07-10 08:30:42,335 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.buying.doctype.supplier_scorecard.supplier_scorecard...., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards)
2025-07-10 08:30:42,579 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards)
2025-07-10 08:30:42,579 Result is kept for 600 seconds
2025-07-10 08:30:42,587 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.controllers.accounts_controller.update_invoice_status..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.controllers.accounts_controller.update_invoice_status)
2025-07-10 08:30:42,902 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.controllers.accounts_controller.update_invoice_status)
2025-07-10 08:30:42,902 Result is kept for 600 seconds
2025-07-10 08:30:42,910 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'propms.auto_custom.statusChangeBeforeLeaseExpire'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::propms.auto_custom.statusChangeBeforeLeaseExpire)
2025-07-10 08:30:43,219 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::propms.auto_custom.statusChangeBeforeLeaseExpire)
2025-07-10 08:30:43,219 Result is kept for 600 seconds
2025-07-10 08:30:43,227 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.form.document_follow.send_daily_updates'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.form.document_follow.send_daily_updates)
2025-07-10 08:30:43,458 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.desk.form.document_follow.send_daily_updates)
2025-07-10 08:30:43,459 Result is kept for 600 seconds
2025-07-10 08:30:43,468 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.job_opening.job_opening.close_expired_job_ope..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings)
2025-07-10 08:30:43,777 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings)
2025-07-10 08:30:43,777 Result is kept for 600 seconds
2025-07-10 08:30:43,786 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.daily_work_summary_group.daily_work_summary_g..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails)
2025-07-10 08:30:44,073 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails)
2025-07-10 08:30:44,074 Result is kept for 600 seconds
2025-07-10 08:30:44,081 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.assets.doctype.asset_maintenance_log.asset_maintenanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status)
2025-07-10 08:30:44,317 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status)
2025-07-10 08:30:44,317 Result is kept for 600 seconds
2025-07-10 08:30:44,325 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.support.doctype.issue.issue.auto_close_tickets'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.support.doctype.issue.issue.auto_close_tickets)
2025-07-10 08:30:44,559 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.support.doctype.issue.issue.auto_close_tickets)
2025-07-10 08:30:44,559 Result is kept for 600 seconds
2025-07-10 08:30:44,567 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.setup.doctype.company.company.cache_companies_monthly..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history)
2025-07-10 08:30:44,868 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history)
2025-07-10 08:30:44,869 Result is kept for 600 seconds
2025-07-10 08:30:44,877 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_a..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles)
2025-07-10 08:30:45,222 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles)
2025-07-10 08:30:45,222 Result is kept for 600 seconds
2025-07-10 08:30:45,232 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:30:45,434 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:30:45,435 Result is kept for 600 seconds
2025-07-10 08:30:45,443 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.hourly_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.hourly_reminder)
2025-07-10 08:30:45,781 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.hourly_reminder)
2025-07-10 08:30:45,781 Result is kept for 600 seconds
2025-07-10 08:30:45,790 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.send_project_status_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.send_project_status_email_to_users)
2025-07-10 08:30:46,122 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.send_project_status_email_to_users)
2025-07-10 08:30:46,122 Result is kept for 600 seconds
2025-07-10 08:30:46,130 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year)
2025-07-10 08:30:46,380 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year)
2025-07-10 08:30:46,380 Result is kept for 600 seconds
2025-07-10 08:30:46,388 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_log.bom_update_log.r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-10 08:30:46,686 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-10 08:30:46,686 Result is kept for 600 seconds
2025-07-10 08:30:46,695 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.project_status_updat..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.project_status_update_reminder)
2025-07-10 08:30:46,986 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.project_status_update_reminder)
2025-07-10 08:30:46,986 Result is kept for 600 seconds
2025-07-10 08:30:46,994 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.collect_project_stat..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.collect_project_status)
2025-07-10 08:30:47,286 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.collect_project_status)
2025-07-10 08:30:47,286 Result is kept for 600 seconds
2025-07-10 08:30:47,295 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.controllers.employee_reminders.send_work_anniversary_rem..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.controllers.employee_reminders.send_work_anniversary_reminders)
2025-07-10 08:30:47,602 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.controllers.employee_reminders.send_work_anniversary_reminders)
2025-07-10 08:30:47,602 Result is kept for 600 seconds
2025-07-10 08:30:47,610 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:30:47,809 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:30:47,810 Result is kept for 600 seconds
2025-07-10 08:30:47,817 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_daily_feedback_remin..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_daily_feedback_reminder)
2025-07-10 08:30:48,046 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_daily_feedback_reminder)
2025-07-10 08:30:48,046 Result is kept for 600 seconds
2025-07-10 08:30:48,054 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:30:48,416 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:30:48,417 Result is kept for 600 seconds
2025-07-10 08:30:48,426 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.utilities.doctype.video.video.update_youtube_data'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.utilities.doctype.video.video.update_youtube_data)
2025-07-10 08:30:48,864 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.utilities.doctype.video.video.update_youtube_data)
2025-07-10 08:30:48,865 Result is kept for 600 seconds
2025-07-10 08:30:48,873 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'propms.lease_invoice_schedule.make_lease_invoice_schedule'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::propms.lease_invoice_schedule.make_lease_invoice_schedule)
2025-07-10 08:30:49,246 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::propms.lease_invoice_schedule.make_lease_invoice_schedule)
2025-07-10 08:30:49,246 Result is kept for 600 seconds
2025-07-10 08:30:49,254 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.utils.run_ledger_health_checks'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.utils.run_ledger_health_checks)
2025-07-10 08:30:49,559 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.accounts.utils.run_ledger_health_checks)
2025-07-10 08:30:49,560 Result is kept for 600 seconds
2025-07-10 08:30:49,568 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.controllers.employee_reminders.send_birthday_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.controllers.employee_reminders.send_birthday_reminders)
2025-07-10 08:30:49,809 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.controllers.employee_reminders.send_birthday_reminders)
2025-07-10 08:30:49,809 Result is kept for 600 seconds
2025-07-10 08:30:49,817 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.doctype.process_statement_of_accounts.proces..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email)
2025-07-10 08:30:50,304 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email)
2025-07-10 08:30:50,304 Result is kept for 600 seconds
2025-07-10 08:30:50,313 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.daily_work_summary_group.daily_work_summary_g..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary)
2025-07-10 08:30:50,595 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary)
2025-07-10 08:30:50,596 Result is kept for 600 seconds
2025-07-10 08:30:50,606 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.selling.doctype.quotation.quotation.set_expired_statu..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.selling.doctype.quotation.quotation.set_expired_status)
2025-07-10 08:30:50,937 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.selling.doctype.quotation.quotation.set_expired_status)
2025-07-10 08:30:50,937 Result is kept for 600 seconds
2025-07-10 08:30:50,946 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.process_download_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-10 08:30:51,346 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-10 08:30:51,346 Result is kept for 600 seconds
2025-07-10 08:30:51,356 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-10 08:30:51,590 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-10 08:30:51,591 Result is kept for 600 seconds
2025-07-10 08:30:51,599 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.utils.auto_create_exchange_rate_revaluation_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily)
2025-07-10 08:30:51,871 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily)
2025-07-10 08:30:51,871 Result is kept for 600 seconds
2025-07-10 08:30:51,881 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.task.task.set_tasks_as_overdue'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.task.task.set_tasks_as_overdue)
2025-07-10 08:30:52,185 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.task.task.set_tasks_as_overdue)
2025-07-10 08:30:52,185 Result is kept for 600 seconds
2025-07-10 08:30:52,193 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.custom_api.create_delivery_note_for_all_pending_sales_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice)
2025-07-10 08:30:52,546 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice)
2025-07-10 08:30:52,547 Result is kept for 600 seconds
2025-07-10 08:30:52,555 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-10 08:30:52,832 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-10 08:30:52,833 Result is kept for 600 seconds
2025-07-10 08:30:52,840 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.additional_salary.generate_additional_sala..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records)
2025-07-10 08:30:53,130 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records)
2025-07-10 08:30:53,130 Result is kept for 600 seconds
2025-07-10 08:30:53,138 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.buying.doctype.supplier_quotation.supplier_quotation...., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status)
2025-07-10 08:30:53,457 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status)
2025-07-10 08:30:53,457 Result is kept for 600 seconds
2025-07-10 08:30:53,465 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.sync_all_stanbank_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-10 08:30:53,800 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-10 08:30:53,800 Result is kept for 600 seconds
2025-07-10 08:30:53,809 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.projects.doctype.project.project.update_project_sales..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.projects.doctype.project.project.update_project_sales_billing)
2025-07-10 08:30:54,114 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.projects.doctype.project.project.update_project_sales_billing)
2025-07-10 08:30:54,114 Result is kept for 600 seconds
2025-07-10 08:30:54,122 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.support.doctype.service_level_agreement.service_level..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status)
2025-07-10 08:30:54,365 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status)
2025-07-10 08:30:54,365 Result is kept for 600 seconds
2025-07-10 08:30:54,373 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.assets.doctype.asset.asset.update_maintenance_status'..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.assets.doctype.asset.asset.update_maintenance_status)
2025-07-10 08:30:54,765 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.assets.doctype.asset.asset.update_maintenance_status)
2025-07-10 08:30:54,765 Result is kept for 600 seconds
2025-07-10 08:30:54,774 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-10 08:30:55,054 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-10 08:30:55,054 Result is kept for 600 seconds
2025-07-10 08:30:55,063 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_che..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin)
2025-07-10 08:30:55,365 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin)
2025-07-10 08:30:55,365 Result is kept for 600 seconds
2025-07-10 08:30:55,372 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.oauth.delete_oauth2_data'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.oauth.delete_oauth2_data)
2025-07-10 08:30:55,644 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.oauth.delete_oauth2_data)
2025-07-10 08:30:55,644 Result is kept for 600 seconds
2025-07-10 08:30:55,652 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.notifications.clear_notifications'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.notifications.clear_notifications)
2025-07-10 08:30:55,900 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.desk.notifications.clear_notifications)
2025-07-10 08:30:55,900 Result is kept for 600 seconds
2025-07-10 08:30:55,908 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_drive.google_drive.daily_b..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_drive.google_drive.daily_backup)
2025-07-10 08:30:56,341 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_drive.google_drive.daily_backup)
2025-07-10 08:30:56,342 Result is kept for 600 seconds
2025-07-10 08:30:56,351 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.web_page.web_page.check_publish_status..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.website.doctype.web_page.web_page.check_publish_status)
2025-07-10 08:30:56,752 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.website.doctype.web_page.web_page.check_publish_status)
2025-07-10 08:30:56,752 Result is kept for 600 seconds
2025-07-10 08:30:56,761 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.user_settings.sync_user_settings'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.user_settings.sync_user_settings)
2025-07-10 08:30:56,995 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.model.utils.user_settings.sync_user_settings)
2025-07-10 08:30:56,995 Result is kept for 600 seconds
2025-07-10 08:30:57,003 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.assets.doctype.asset.depreciation.post_depreciation_e..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.assets.doctype.asset.depreciation.post_depreciation_entries)
2025-07-10 08:30:57,427 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.assets.doctype.asset.depreciation.post_depreciation_entries)
2025-07-10 08:30:57,427 Result is kept for 600 seconds
2025-07-10 08:30:57,435 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.utils.generate_leave_encashment'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.utils.generate_leave_encashment)
2025-07-10 08:30:57,777 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.utils.generate_leave_encashment)
2025-07-10 08:30:57,777 Result is kept for 600 seconds
2025-07-10 08:30:57,786 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation)
2025-07-10 08:30:58,018 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation)
2025-07-10 08:30:58,019 Result is kept for 600 seconds
2025-07-10 08:30:58,027 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.personal_data_deletion_request.persona..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request)
2025-07-10 08:30:58,359 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request)
2025-07-10 08:30:58,359 Result is kept for 600 seconds
2025-07-10 08:30:58,368 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.stock.doctype.repost_item_valuation.repost_item_valua..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries)
2025-07-10 08:30:58,687 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries)
2025-07-10 08:30:58,688 Result is kept for 600 seconds
2025-07-10 08:30:58,696 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.s3_backup_settings.s3_backup_sett..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily)
2025-07-10 08:30:59,153 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily)
2025-07-10 08:30:59,154 Result is kept for 600 seconds
2025-07-10 08:30:59,162 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.crm.utils.open_leads_opportunities_based_on_todays_ev..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.crm.utils.open_leads_opportunities_based_on_todays_event)
2025-07-10 08:30:59,400 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.crm.utils.open_leads_opportunities_based_on_todays_event)
2025-07-10 08:30:59,400 Result is kept for 600 seconds
2025-07-10 08:30:59,408 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.setup.doctype.email_digest.email_digest.send'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.setup.doctype.email_digest.email_digest.send)
2025-07-10 08:30:59,670 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.setup.doctype.email_digest.email_digest.send)
2025-07-10 08:30:59,670 Result is kept for 600 seconds
2025-07-10 08:30:59,677 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.twofactor.delete_all_barcodes_for_users'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.twofactor.delete_all_barcodes_for_users)
2025-07-10 08:30:59,946 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.twofactor.delete_all_barcodes_for_users)
2025-07-10 08:30:59,946 Result is kept for 600 seconds
2025-07-10 08:30:59,955 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.core.doctype.log_settings.log_settings.run_log_clean_u..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.core.doctype.log_settings.log_settings.run_log_clean_up)
2025-07-10 08:31:00,713 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.core.doctype.log_settings.log_settings.run_log_clean_up)
2025-07-10 08:31:00,714 Result is kept for 600 seconds
2025-07-10 08:31:00,723 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assi..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation)
2025-07-10 08:31:01,030 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation)
2025-07-10 08:31:01,030 Result is kept for 600 seconds
2025-07-10 08:31:01,039 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry)
2025-07-10 08:31:01,445 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry)
2025-07-10 08:31:01,445 Result is kept for 600 seconds
2025-07-10 08:31:01,455 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.shift_type.shift_type.process_auto_attendance..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts)
2025-07-10 08:31:01,717 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts)
2025-07-10 08:31:01,717 Result is kept for 600 seconds
2025-07-10 08:31:01,726 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.social.doctype.energy_point_settings.energy_point_sett..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points)
2025-07-10 08:31:02,069 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points)
2025-07-10 08:31:02,069 Result is kept for 600 seconds
2025-07-10 08:31:02,078 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.personal_data_deletion_request.persona..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record)
2025-07-10 08:31:02,309 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record)
2025-07-10 08:31:02,309 Result is kept for 600 seconds
2025-07-10 08:31:02,317 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.sessions.clear_expired_sessions'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.sessions.clear_expired_sessions)
2025-07-10 08:31:02,582 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.sessions.clear_expired_sessions)
2025-07-10 08:31:02,582 Result is kept for 600 seconds
2025-07-10 08:31:02,591 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms)
2025-07-10 08:31:02,878 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms)
2025-07-10 08:31:02,878 Result is kept for 600 seconds
2025-07-10 08:31:02,886 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.utils.allocate_earned_leaves'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.utils.allocate_earned_leaves)
2025-07-10 08:31:03,173 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::hrms.hr.utils.allocate_earned_leaves)
2025-07-10 08:31:03,174 Result is kept for 600 seconds
2025-07-10 08:31:03,182 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.accounts.doctype.process_subscription.process_subscri..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process)
2025-07-10 08:31:03,598 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process)
2025-07-10 08:31:03,598 Result is kept for 600 seconds
2025-07-10 08:31:03,606 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.core.doctype.prepared_report.prepared_report.expire_st..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report)
2025-07-10 08:31:03,837 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report)
2025-07-10 08:31:03,837 Result is kept for 600 seconds
2025-07-10 08:31:03,844 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.utilities.bulk_transaction.retry'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.utilities.bulk_transaction.retry)
2025-07-10 08:31:04,132 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::erpnext.utilities.bulk_transaction.retry)
2025-07-10 08:31:04,133 Result is kept for 600 seconds
2025-07-10 08:31:04,141 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.form.document_follow.send_hourly_updates'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.form.document_follow.send_hourly_updates)
2025-07-10 08:31:04,376 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.desk.form.document_follow.send_hourly_updates)
2025-07-10 08:31:04,376 Result is kept for 600 seconds
2025-07-10 08:31:04,385 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.auto_email_report.auto_email_report.send..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.auto_email_report.auto_email_report.send_daily)
2025-07-10 08:31:05,066 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.email.doctype.auto_email_report.auto_email_report.send_daily)
2025-07-10 08:31:05,066 Result is kept for 600 seconds
2025-07-10 08:31:05,077 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_contacts.google_contacts.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_contacts.google_contacts.sync)
2025-07-10 08:31:05,525 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_contacts.google_contacts.sync)
2025-07-10 08:31:05,525 Result is kept for 600 seconds
2025-07-10 08:31:05,535 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.dropbox_settings.dropbox_settings..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily)
2025-07-10 08:31:06,095 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily)
2025-07-10 08:31:06,096 Result is kept for 600 seconds
2025-07-10 08:31:06,106 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.page.backups.backups.delete_downloadable_backups'..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.desk.page.backups.backups.delete_downloadable_backups)
2025-07-10 08:31:06,335 home-imetumba-dev-mybench:long: Job OK (mysite::scheduled_job::frappe.desk.page.backups.backups.delete_downloadable_backups)
2025-07-10 08:31:06,335 Result is kept for 600 seconds
2025-07-10 08:31:06,343 home-imetumba-dev-mybench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.automation.doctype.auto_repeat.auto_repeat.create_repeated_entries', kwargs={'data': []}, method='frappe.automation.doctype.auto_repeat.auto_repeat.create_repeated_entries', site='mysite', user='Administrator') (mysite::f2ce9397-cf2a-47c6-854a-40c55bc3e72f)
2025-07-10 08:31:06,530 home-imetumba-dev-mybench:long: Job OK (mysite::f2ce9397-cf2a-47c6-854a-40c55bc3e72f)
2025-07-10 08:31:06,530 Result is kept for 600 seconds
2025-07-10 08:32:32,625 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:32:32,858 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:32:32,858 Result is kept for 600 seconds
2025-07-10 08:32:32,867 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:32:33,247 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:32:33,248 Result is kept for 600 seconds
2025-07-10 08:32:33,257 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:32:33,665 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:32:33,666 Result is kept for 600 seconds
2025-07-10 08:32:33,674 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:32:33,903 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:32:33,903 Result is kept for 600 seconds
2025-07-10 08:32:33,912 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:32:34,317 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:32:34,317 Result is kept for 600 seconds
2025-07-10 08:36:33,801 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:36:34,025 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:36:34,025 Result is kept for 600 seconds
2025-07-10 08:36:34,033 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:36:34,440 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:36:34,441 Result is kept for 600 seconds
2025-07-10 08:36:34,451 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:36:34,801 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:36:34,801 Result is kept for 600 seconds
2025-07-10 08:36:34,810 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:36:35,183 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:36:35,184 Result is kept for 600 seconds
2025-07-10 08:36:35,194 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:36:35,406 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:36:35,406 Result is kept for 600 seconds
2025-07-10 08:40:34,820 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:40:35,050 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:40:35,051 Result is kept for 600 seconds
2025-07-10 08:40:35,059 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-10 08:40:35,339 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-10 08:40:35,339 Result is kept for 600 seconds
2025-07-10 08:40:35,348 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:40:35,701 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:40:35,701 Result is kept for 600 seconds
2025-07-10 08:40:35,710 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:40:35,926 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:40:35,926 Result is kept for 600 seconds
2025-07-10 08:40:35,934 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:40:36,292 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:40:36,292 Result is kept for 600 seconds
2025-07-10 08:40:36,301 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:40:36,694 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:40:36,694 Result is kept for 600 seconds
2025-07-10 08:44:35,832 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:44:36,238 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:44:36,239 Result is kept for 600 seconds
2025-07-10 08:44:36,247 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:44:36,613 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:44:36,613 Result is kept for 600 seconds
2025-07-10 08:44:36,622 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:44:36,856 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:44:36,856 Result is kept for 600 seconds
2025-07-10 08:44:36,864 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:44:37,097 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:44:37,097 Result is kept for 600 seconds
2025-07-10 08:44:37,107 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:44:37,519 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:44:37,519 Result is kept for 600 seconds
2025-07-10 08:45:36,093 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.process_download_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-10 08:45:36,543 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.process_download_files)
2025-07-10 08:45:36,543 Result is kept for 600 seconds
2025-07-10 08:45:36,553 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.csftz_hooks.items_revaluation.process_incorrect_balanc..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-10 08:45:36,817 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty)
2025-07-10 08:45:36,818 Result is kept for 600 seconds
2025-07-10 08:45:36,827 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-10 08:45:37,069 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.deferred_insert.save_to_db)
2025-07-10 08:45:37,070 Result is kept for 600 seconds
2025-07-10 08:45:37,078 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-10 08:45:37,365 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-07-10 08:45:37,365 Result is kept for 600 seconds
2025-07-10 08:45:37,374 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-10 08:45:37,619 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-07-10 08:45:37,619 Result is kept for 600 seconds
2025-07-10 08:45:37,631 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-10 08:45:37,880 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-07-10 08:45:37,881 Result is kept for 600 seconds
2025-07-10 08:45:37,889 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'erpnext.manufacturing.doctype.bom_update_log.bom_update_log.r..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-10 08:45:38,151 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs)
2025-07-10 08:45:38,151 Result is kept for 600 seconds
2025-07-10 08:45:38,159 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-10 08:45:38,444 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-07-10 08:45:38,444 Result is kept for 600 seconds
2025-07-10 08:45:38,453 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'csf_tz.stanbic.sftp.sync_all_stanbank_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-10 08:45:38,795 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::csf_tz.stanbic.sftp.sync_all_stanbank_files)
2025-07-10 08:45:38,795 Result is kept for 600 seconds
2025-07-10 08:48:36,892 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:48:37,180 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:48:37,180 Result is kept for 600 seconds
2025-07-10 08:48:37,192 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:48:39,111 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:48:39,111 Result is kept for 600 seconds
2025-07-10 08:48:39,122 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:48:39,862 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:48:39,862 Result is kept for 600 seconds
2025-07-10 08:48:39,893 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:48:40,251 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:48:40,251 Result is kept for 600 seconds
2025-07-10 08:48:40,277 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:48:40,766 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:48:40,767 Result is kept for 600 seconds
2025-07-10 08:50:37,771 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-10 08:50:38,075 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-07-10 08:50:38,076 Result is kept for 600 seconds
2025-07-10 08:50:38,084 Cleaning registries for queue: home-imetumba-dev-mybench:short
2025-07-10 08:50:38,085 Cleaning registries for queue: home-imetumba-dev-mybench:default
2025-07-10 08:50:38,087 Cleaning registries for queue: home-imetumba-dev-mybench:long
2025-07-10 08:52:38,104 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'payments.payment_gateways.doctype.razorpay_settings.razorpay_..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:52:38,651 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment)
2025-07-10 08:52:38,651 Result is kept for 600 seconds
2025-07-10 08:52:38,664 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:52:39,204 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-07-10 08:52:39,204 Result is kept for 600 seconds
2025-07-10 08:52:39,218 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:52:39,513 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.monitor.flush)
2025-07-10 08:52:39,513 Result is kept for 600 seconds
2025-07-10 08:52:39,523 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'hrms.hr.doctype.interview.interview.send_interview_reminder'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:52:39,777 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::hrms.hr.doctype.interview.interview.send_interview_reminder)
2025-07-10 08:52:39,777 Result is kept for 600 seconds
2025-07-10 08:52:39,790 home-imetumba-dev-mybench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='mysite', user='Administrator') (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:52:40,260 home-imetumba-dev-mybench:default: Job OK (mysite::scheduled_job::frappe.email.queue.flush)
2025-07-10 08:52:40,261 Result is kept for 600 seconds
